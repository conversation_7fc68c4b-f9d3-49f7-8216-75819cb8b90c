#include "mydefine.h"

// 简单测试函数 - 验证uint和int智能提示
void test_types_simple(void)
{
    // 在下面输入 uint 应该看到：uint8_t, uint16_t, uint32_t
    uint8_t data = 255;
    uint16_t counter = 1000;
    uint32_t timestamp = 0;
    
    // 在下面输入 int 应该看到：int8_t, int16_t, int32_t
    int8_t temp = -50;
    int16_t result = -1000;
    int32_t value = -50000;
    
    // 测试基本类型
    int a = 10;
    unsigned int b = 20;
    
    // 测试布尔类型
    bool flag = true;
    
    // 在这里输入 uint 或 int 测试智能提示
}
