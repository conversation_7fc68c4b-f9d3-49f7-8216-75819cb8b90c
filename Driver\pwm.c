#include <pwm.h>



void pwm_init(void)
{
RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM2,ENABLE);
RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);

//GPIO_PinRemapConfig() 

GPIO_InitTypeDef 	GPIO_Initstructure;
GPIO_Initstructure.GPIO_Mode=GPIO_Mode_AF_PP;
GPIO_Initstructure.GPIO_Pin=GPIO_Pin_0|GPIO_Pin_1|GPIO_Pin_2; // PA0(CH1) + PA1(CH2)
GPIO_Initstructure.GPIO_Speed=GPIO_Speed_50MHz;
GPIO_Init(GPIOA,&GPIO_Initstructure);
	
	
TIM_InternalClockConfig(TIM2);

TIM_TimeBaseInitTypeDef TIM_TimeBasenitstructure;
TIM_TimeBasenitstructure.TIM_ClockDivision=TIM_CKD_DIV1;
TIM_TimeBasenitstructure.TIM_CounterMode=TIM_CounterMode_Up;
TIM_TimeBasenitstructure.TIM_Period=20000-1;
TIM_TimeBasenitstructure.TIM_Prescaler=72-1;
TIM_TimeBasenitstructure.TIM_RepetitionCounter=0;
TIM_TimeBaseInit(TIM2,&TIM_TimeBasenitstructure);

TIM_OCInitTypeDef TIM_OutputCompareInitstructure;
TIM_OCStructInit(&TIM_OutputCompareInitstructure);
TIM_OutputCompareInitstructure.TIM_OCMode=TIM_OCMode_PWM1;
TIM_OutputCompareInitstructure.TIM_OCPolarity=TIM_OCPolarity_High;
TIM_OutputCompareInitstructure.TIM_OutputState=TIM_OutputState_Enable;
TIM_OutputCompareInitstructure.TIM_Pulse=50;	 //CCR
TIM_OC1Init(TIM2,&TIM_OutputCompareInitstructure);	

TIM_OutputCompareInitstructure.TIM_Pulse=0;	
TIM_OC2Init(TIM2,&TIM_OutputCompareInitstructure);	

TIM_OutputCompareInitstructure.TIM_Pulse=0;	 
TIM_OC3Init(TIM2,&TIM_OutputCompareInitstructure);	

TIM_Cmd(TIM2,ENABLE);	
}



void pwm_setcompare_1(uint16_t compare)
{
TIM_SetCompare1(TIM2,compare);
}




//舵机周期20ms, 1.5ms中立位置, 0.5ms-2.5ms范围
// 0.5ms = 500us = 50 * 10us,

void set_servoangel(float angel) //
{
uint16_t compare2=angel/180.0*2000+500;
TIM_SetCompare2(TIM2,compare2);
}

//一个pwm两个gpio控制电机
void motor_init(void)
{
RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);
GPIO_InitTypeDef GPIO_Initstructure;
GPIO_Initstructure.GPIO_Mode=GPIO_Mode_Out_PP;
GPIO_Initstructure.GPIO_Pin=GPIO_Pin_3|GPIO_Pin_4;
GPIO_Initstructure.GPIO_Speed=GPIO_Speed_50MHz;
GPIO_Init(GPIOA,&GPIO_Initstructure);   

pwm_init();
}


void set_motor_speed(int8_t speed)
{
if(speed>0)
{
  GPIO_SetBits(GPIOA,GPIO_Pin_3); 
  GPIO_ResetBits(GPIOA,GPIO_Pin_4); 
}
else if(speed<0)
{
  GPIO_SetBits(GPIOA,GPIO_Pin_4); 
  GPIO_ResetBits(GPIOA,GPIO_Pin_3); 
  speed = -speed; 
}
else
{
  GPIO_ResetBits(GPIOA,GPIO_Pin_3); 
  GPIO_ResetBits(GPIOA,GPIO_Pin_4); 
}

TIM_SetCompare3(TIM2,speed);
}





void IC_init(void)

{

RCC_APB1PeriphClockCmd (RCC_APB1Periph_TIM3,ENABLE);
RCC_APB2PeriphClockCmd (RCC_APB2Periph_GPIOA,ENABLE);

GPIO_InitTypeDef GPIO_Initstructure;
GPIO_Initstructure.GPIO_Mode=GPIO_Mode_IPU;
GPIO_Initstructure.GPIO_Pin=GPIO_Pin_6;
GPIO_Initstructure.GPIO_Speed=GPIO_Speed_50MHz;
GPIO_Init(GPIOA ,&GPIO_Initstructure);

TIM_InternalClockConfig(TIM3);
TIM_TimeBaseInitTypeDef TIM_TimeBaseInitstructure;
TIM_TimeBaseInitstructure.TIM_ClockDivision=TIM_CKD_DIV1;
TIM_TimeBaseInitstructure.TIM_CounterMode=TIM_CounterMode_Up;
TIM_TimeBaseInitstructure.TIM_Period=65536-1;
TIM_TimeBaseInitstructure.TIM_Prescaler=72-1;
TIM_TimeBaseInitstructure.TIM_RepetitionCounter=0;
TIM_TimeBaseInit(TIM3,&TIM_TimeBaseInitstructure);

TIM_ICInitTypeDef TIM_InputCaptureInitstructure;
TIM_InputCaptureInitstructure.TIM_Channel=TIM_Channel_1;
TIM_InputCaptureInitstructure.TIM_ICFilter=0xF;
TIM_InputCaptureInitstructure.TIM_ICPolarity=TIM_ICPolarity_Rising;
TIM_InputCaptureInitstructure.TIM_ICPrescaler=TIM_ICPSC_DIV1;
TIM_InputCaptureInitstructure.TIM_ICSelection=TIM_ICSelection_DirectTI;
TIM_ICInit(TIM3 ,&TIM_InputCaptureInitstructure);
//TIM_PWMIConfig(TIM3 ,&TIM_InputCaptureInitstructure);//设置pwmi模式


TIM_SelectInputTrigger(TIM3,TIM_TS_TI1FP1);

TIM_SelectSlaveMode(TIM3,TIM_SlaveMode_Reset);

TIM_Cmd(TIM3,ENABLE);
}



uint32_t IC_getfreq(void)

{
return 1000000/TIM_GetCapture1(TIM3);
} 


uint32_t IC_getduty(void)
{
return TIM_GetCapture2(TIM3)*100/TIM_GetCapture1(TIM3);//通道二得到ccr2高电平时间 通道一得到ccr1周期时间

} 



