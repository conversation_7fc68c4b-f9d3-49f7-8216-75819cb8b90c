{"configurations": [{"name": "Win32", "includePath": ["${workspaceFolder}/**"], "defines": ["_DEBUG", "UNICODE", "_UNICODE"], "windowsSdkVersion": "10.0.22621.0", "compilerPath": "cl.exe", "cStandard": "c17", "cppStandard": "c++17", "intelliSenseMode": "windows-msvc-x64"}, {"name": "demo_01", "includePath": ["${workspaceFolder}/Start", "${workspaceFolder}/User", "${workspaceFolder}/Library", "${workspaceFolder}/Driver", "${workspaceFolder}/component", "${workspaceFolder}/System", "${workspaceFolder}", "${workspaceFolder}/**", "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/include", "C:/Program Files (x86)/Windows Kits/10/Include/10.0.19041.0/ucrt"], "defines": ["USE_STDPERIPH_DRIVER", "STM32F10X_MD"], "intelliSenseMode": "windows-msvc-x64", "compilerPath": "C:/Program Files (x86)/Microsoft Visual Studio/2019/Community/VC/Tools/MSVC/14.29.30133/bin/Hostx64/x64/cl.exe", "cStandard": "c99", "cppStandard": "c++11", "forcedInclude": ["${workspaceFolder}/.vscode/force_includes.h"], "browse": {"path": ["${workspaceFolder}/Start", "${workspaceFolder}/User", "${workspaceFolder}/Library", "${workspaceFolder}/Driver", "${workspaceFolder}/component", "${workspaceFolder}/System", "${workspaceFolder}", "${workspaceFolder}/**"], "limitSymbolsToIncludedHeaders": false, "databaseFilename": "${workspaceFolder}/.vscode/browse.vc.db"}}], "version": 4}