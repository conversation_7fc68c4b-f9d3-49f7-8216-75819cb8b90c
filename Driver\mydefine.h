// 标准C库头文件 - 确保基本类型智能提示
#include <stdint.h>    // uint8_t, uint16_t, uint32_t, int8_t, int16_t, int32_t
#include <stdio.h>     // printf, scanf等标准输入输出
#include <stdarg.h>    // 可变参数列表
#include <string.h>    // strlen, strcpy等字符串函数
#include <stdlib.h>    // malloc, free等内存管理
#include <stdbool.h>   // bool, true, false

// STM32核心头文件
#include "stm32f10x.h"
#include "stm32f10x_rcc.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_tim.h"

// 项目驱动头文件
#include "scheduler.h"
#include "led.h"
#include "systick.h"
#include "key.h"
#include "OLED.h"
#include "oled_app.h"
#include <sensorcounter.h>
#include <Timer.h>
#include <pwm.h>
#include <encoder.h>



extern uint8_t led2_lightflag;
extern uint8_t ucled[3];
extern uint8_t beep_enable;
extern uint8_t time_200ms;
extern uint8_t led2flag;
extern uint16_t num;
extern uint8_t angel;









