#include <scheduler.h>
#include "stm32f10x.h"  
#include <misc.h>
#include <led.h>
#include <systick.h>
#include <key.h>
#include <OLED.h>
#include <oled_app.h>
#include <stdio.h>   
#include <stdarg.h>  
#include <stdint.h>  
#include <sensorcounter.h>
#include <Timer.h>
#include <pwm.h>
#include <encoder.h>



extern uint8_t led2_lightflag;
extern uint8_t ucled[3];
extern uint8_t beep_enable;
extern uint8_t time_200ms;
extern uint8_t led2flag;
extern uint16_t num;
extern uint8_t angel;









