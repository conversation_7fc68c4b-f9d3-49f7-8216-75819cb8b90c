#include <key.h>


uint8_t key_val,key_old,key_up,key_down;
uint8_t angel;
int8_t speed=-100;

void key_gpio_init(void)
{
RCC_APB2PeriphClockCmd (RCC_APB2Periph_GPIOA ,ENABLE);
GPIO_InitTypeDef GPIO_Initstructure;
GPIO_Initstructure.GPIO_Mode=  GPIO_Mode_IPU;	
//GPIO_Initstructure.GPIO_Pin=GPIO_Pin_5|GPIO_Pin_6;
	GPIO_Initstructure.GPIO_Pin=GPIO_Pin_5;
GPIO_Initstructure.GPIO_Speed= GPIO_Speed_50MHz;
GPIO_Init(GPIOA,&GPIO_Initstructure);
}


uint8_t key_read(void)
{
 uint8_t temp=0;
	
 if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_5)==0) temp=1;
 if(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_6)==0) temp=2;
 if((GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_5)==0)&&(GPIO_ReadInputDataBit(GPIOA,GPIO_Pin_6)==0)) temp=12;
return temp;
}

void key_task(void)
{
 key_val=key_read();
 key_down=key_val&(key_val^key_old);
 key_up=~key_val&(key_val^key_old);
 key_old=key_val;

switch(key_down)
{
	case 1:
		angel+=10;
		if(angel>180) angel=0;
		set_servoangel(angel);
	 
	break;

  case 2:
     speed+=10;
	 if(speed>100) speed=-100;
	  set_motor_speed(speed);
	  //ifled2flag^=1;
		//beep_enable^=1;
	break ;
	
	case 12:
		ucled[2]^=1;
	break;
}

}



