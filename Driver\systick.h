#ifndef __SYSTICK_H__
#define __SYSTICK_H__

#include "mydefine.h"

// =============================================================
//                    系统滴答定时器头文件 (systick.h)
// =============================================================

void SysTick_Init(void);        // 初始化SysTick定时器
uint32_t GetTick(void);         // 获取系统运行毫秒数
void Delay_ms(uint32_t ms);     // 毫秒级延时函数
void SysTick_Increment(void);   // SysTick中断服务函数

#endif




