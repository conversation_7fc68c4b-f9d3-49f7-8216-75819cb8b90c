#include "encoder.h"


void encoder_init(void)
{


RCC_APB2PeriphClockCmd(RCC_APB2Periph_GPIOA,ENABLE);
RCC_APB1PeriphClockCmd(RCC_APB1Periph_TIM3,ENABLE);

GPIO_InitTypeDef GPIO_Initstructure;
GPIO_Initstructure.GPIO_Mode=GPIO_Mode_IPU;	
GPIO_Initstructure.GPIO_Pin=GPIO_Pin_6|GPIO_Pin_7;
GPIO_Initstructure.GPIO_Speed=GPIO_Speed_50MHz;
GPIO_Init(GPIOA,&GPIO_Initstructure);

//编码器接口会托管时钟，所以不需要配置时钟
TIM_TimeBaseInitTypeDef TIM_TimeBaseInitstructure;
TIM_TimeBaseInitstructure.TIM_ClockDivision= TIM_CKD_DIV1; 	
TIM_TimeBaseInitstructure.TIM_CounterMode=TIM_CounterMode_Up; //计数方向也会被编码器接口托管
TIM_TimeBaseInitstructure.TIM_Period=65536-1;
TIM_TimeBaseInitstructure.TIM_Prescaler=1-1;
TIM_TimeBaseInitstructure.TIM_RepetitionCounter=0;
TIM_TimeBaseInit (TIM3,&TIM_TimeBaseInitstructure);
	

TIM_ICInitTypeDef TIM_InputCaptureInitstructure;
TIM_ICStructInit(&TIM_InputCaptureInitstructure); 
TIM_InputCaptureInitstructure.TIM_Channel=TIM_Channel_1;	
TIM_InputCaptureInitstructure.TIM_ICFilter= 0xF;
//TIM_InputCaptureInitstructure.TIM_ICPolarity=TIM_ICPolarity_Rising; 	
TIM_ICInit(TIM3 ,&TIM_InputCaptureInitstructure);

TIM_InputCaptureInitstructure.TIM_Channel=TIM_Channel_2;	
TIM_InputCaptureInitstructure.TIM_ICFilter= 0xF;
//TIM_InputCaptureInitstructure.TIM_ICPolarity=TIM_ICPolarity_Rising; 	
TIM_ICInit(TIM3 ,&TIM_InputCaptureInitstructure);

TIM_EncoderInterfaceConfig(TIM3,TIM_EncoderMode_TI12,TIM_ICPolarity_Rising,TIM_ICPolarity_Rising);


TIM_Cmd(TIM3,ENABLE); //使能定时器

}


int16_t encoder_get_count(void)
{
  return TIM_GetCounter(TIM3);
}




 

