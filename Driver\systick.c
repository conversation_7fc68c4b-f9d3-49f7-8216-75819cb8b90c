#include <systick.h>

// =============================================================
//               �Ƽ�������ʵ�ַ��� (systick.c)
// =============================================================

#include "stm32f10x.h" // �������оƬ��Ӧ��ͷ�ļ�

// ����һ������������ȫ�ֽ��ı���
static __IO uint32_t sysTick_counter = 0;

/**
 * @brief  ����SysTick��ʱ��Ϊ1ms�ж�һ��
 * @param  None
 * @retval None
 */
void SysTick_Init(void) {
    if (SysTick_Config(SystemCoreClock / 1000)) {
        while (1);
    }
}

/**
 * @brief  SysTick�жϷ�������ÿ1ms����һ��
 * @param  None
 * @retval None
 */
void SysTick_Increment(void) {
    sysTick_counter++; // ȫ�ֱ�������
}

/**
 * @brief  ��ȡ��ϵͳ���������ĺ����� (���Ч�� HAL_GetTick)
 * @param  None
 * @retval uint32_t ϵͳ�ĺ������
 */
uint32_t GetTick(void) {
    return sysTick_counter;
}

/**
 * @brief  ���뼶��ʱ (����GetTickʵ��)
 * @param  ms: ��Ҫ��ʱ�ĺ�����
 * @retval None
 */
void Delay_ms(uint32_t ms) {
    uint32_t start_tick = GetTick();
    // �ȴ���ֱ����ǰ��������ʼ���ĵĲ�ֵ�ﵽָ������ʱʱ��
    // (uint32_t)(GetTick() - start_tick) < ms ����д�����Է�ֹ sysTick_counter ������µ�����
    while ((uint32_t)(GetTick() - start_tick) < ms) {
        // �ȴ�
    }
}



