// VSCode强制包含文件 - 解决智能提示问题
#ifndef __FORCE_INCLUDES_H
#define __FORCE_INCLUDES_H

// 定义必要的宏
#define USE_STDPERIPH_DRIVER
#define STM32F10X_MD

// 标准C库
#include <stdint.h>
#include <stdio.h>
#include <stdarg.h>

// STM32核心头文件
#include "stm32f10x.h"
#include "stm32f10x_conf.h"

// STM32外设库头文件
#include "stm32f10x_rcc.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_tim.h"
#include "stm32f10x_usart.h"
#include "stm32f10x_spi.h"
#include "stm32f10x_i2c.h"
#include "stm32f10x_adc.h"
#include "stm32f10x_dma.h"
#include "stm32f10x_exti.h"
#include "misc.h"

#endif // __FORCE_INCLUDES_H
