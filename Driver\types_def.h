#ifndef __TYPES_DEF_H__
#define __TYPES_DEF_H__

// 标准整数类型定义 - 确保VSCode智能提示
typedef unsigned char uint8_t;
typedef unsigned short uint16_t;
typedef unsigned int uint32_t;
typedef unsigned long long uint64_t;

typedef signed char int8_t;
typedef signed short int16_t;
typedef signed int int32_t;
typedef signed long long int64_t;

// 其他常用类型
typedef unsigned char bool;
typedef unsigned int size_t;

// 常用宏定义
#ifndef true
#define true 1
#endif

#ifndef false
#define false 0
#endif

#ifndef NULL
#define NULL ((void*)0)
#endif

#endif // __TYPES_DEF_H__
