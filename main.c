#include "stm32f10x.h"             // Device header
#include <mydefine.h>

uint16_t compare;

uint16_t num;


int main(void)
{  
 OLED_Init();
 pwm_init();
//IC_init();
encoder_init();	
 //motor_init();
 sensorcounter_init();
 led_gpio_init();
 key_gpio_init();
 beep_gpio_init();
 scheduler_init();
 SysTick_Init();
Timer_init();
while(1)
{
scheduler_run();
//compare=(compare+1)%100;
//	pwm_setcompare_1(compare);
}
} 





void TIM2_IRQHandler(void)
{
if(TIM_GetITStatus(TIM2,TIM_IT_Update)==SET)
{

 if(led2flag)
{
  if(++time_200ms==200)
 {	
	time_200ms=0;
  led2_lightflag^=1;
 }
}
else 
	{
	time_200ms=0;
	led2_lightflag=0;
	}

compare=(compare+1)%100;
pwm_setcompare_1(compare);
	
	num++;

TIM_ClearITPendingBit(TIM2, TIM_IT_Update);
}
}








