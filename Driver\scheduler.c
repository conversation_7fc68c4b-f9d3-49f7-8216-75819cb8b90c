#include <scheduler.h>

typedef struct 
{
void(*task_func)(void);
uint32_t rate_ms;
uint32_t last_ms;	
}task_t;

static task_t scheduler_task[]=
{
{led_task,1,0},
{key_task,10,0},
{oled_task,50,0},
};

uint8_t task_num;

void scheduler_init(void)
{
task_num =sizeof (scheduler_task )/sizeof (task_t );
}


void scheduler_run(void )
{
 uint8_t i=0;
	for(i=0;i<task_num ;i++)
	{
	  uint32_t now=GetTick ();
		if(now >=scheduler_task[i].rate_ms +scheduler_task [i].last_ms )
		{
		 scheduler_task[i] .last_ms=now  ;
			scheduler_task [i].task_func ();
		}
	}
}





