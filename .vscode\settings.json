{"C_Cpp.errorSquiggles": "disabled", "C_Cpp.intelliSenseEngine": "default", "C_Cpp.autocomplete": "default", "C_Cpp.suggestSnippets": true, "C_Cpp.default.configurationProvider": "ms-vscode.cpptools", "C_Cpp.intelliSenseUpdateDelay": 100, "C_Cpp.workspaceParsingPriority": "highest", "C_Cpp.enhancedColorization": "enabled", "C_Cpp.default.browse.limitSymbolsToIncludedHeaders": false, "C_Cpp.default.systemIncludePath": [], "C_Cpp.configurationWarnings": "disabled", "C_Cpp.inactiveRegionOpacity": 0.55, "C_Cpp.dimInactiveRegions": true, "C_Cpp.intelliSenseEngineFallback": "enabled", "C_Cpp.loggingLevel": "Debug", "C_Cpp.default.compilerArgs": ["-std=c99"], "editor.quickSuggestions": {"other": true, "comments": false, "strings": false}, "editor.quickSuggestionsDelay": 0, "editor.suggestOnTriggerCharacters": true, "editor.wordBasedSuggestions": "matchingDocuments", "files.associations": {"*.h": "c", "*.c": "c", "stdint.h": "c", "stdio.h": "c", "stdlib.h": "c", "string.h": "c", "stdbool.h": "c", "stm32f10x.h": "c", "stm32f10x_rcc.h": "c", "stm32f10x_gpio.h": "c", "stm32f10x_tim.h": "c", "stm32f10x_usart.h": "c", "stm32f10x_spi.h": "c", "stm32f10x_i2c.h": "c", "stm32f10x_adc.h": "c", "misc.h": "c"}}