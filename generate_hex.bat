@echo off
echo 正在尝试生成 demo_01.hex 文件...
echo.

REM 尝试使用 Keil UV4 编译
if exist "C:\Keil_v5\UV4\UV4.exe" (
    echo 找到 Keil UV4，正在编译...
    "C:\Keil_v5\UV4\UV4.exe" -b demo_01.uvprojx
    goto :check_result
)

if exist "C:\Keil\ARM\BIN40\UV4.exe" (
    echo 找到 Keil UV4，正在编译...
    "C:\Keil\ARM\BIN40\UV4.exe" -b demo_01.uvprojx
    goto :check_result
)

REM 尝试使用 ARM GCC 工具链转换 axf 文件
if exist "Objects\demo_01.axf" (
    echo 尝试使用 arm-none-eabi-objcopy 转换 axf 文件...
    arm-none-eabi-objcopy -O ihex Objects\demo_01.axf Objects\demo_01.hex
    if exist "Objects\demo_01.hex" (
        copy "Objects\demo_01.hex" "demo_01.hex"
        echo 成功生成 demo_01.hex 文件！
        goto :end
    )
)

echo.
echo 无法自动生成 hex 文件。请尝试以下方法：
echo 1. 在 Keil IDE 中打开项目并重新编译
echo 2. 确保项目设置中启用了 "Create HEX File" 选项
echo 3. 编译完成后，hex 文件将在 Objects 目录中生成
echo.
goto :end

:check_result
if exist "Objects\demo_01.hex" (
    copy "Objects\demo_01.hex" "demo_01.hex"
    echo 成功生成 demo_01.hex 文件！
) else (
    echo 编译完成，但未找到 hex 文件。请检查编译日志。
)

:end
pause
