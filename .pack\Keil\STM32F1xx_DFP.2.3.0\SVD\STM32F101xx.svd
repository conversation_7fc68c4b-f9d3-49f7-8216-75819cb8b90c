<?xml version="1.0" encoding="utf-8" standalone="no"?>
<device schemaVersion="1.1"
xmlns:xs="http://www.w3.org/2001/XMLSchema-instance"
xs:noNamespaceSchemaLocation="CMSIS-SVD_Schema_1_1.xsd">
  <name>STM32F101xx</name>
  <version>1.1</version>
  <description>STM32F101xx</description>
  <!--Bus Interface Properties-->
  <!--Cortex-M3 is byte addressable-->
  <addressUnitBits>8</addressUnitBits>
  <!--the maximum data bit width accessible within a single transfer-->
  <width>32</width>
  <!--Register Default Properties-->
  <size>0x20</size>
  <resetValue>0x0</resetValue>
  <resetMask>0xFFFFFFFF</resetMask>
  <peripherals>
    <peripheral>
      <name>FSMC</name>
      <description>Flexible static memory controller</description>
      <groupName>FSMC</groupName>
      <baseAddress>0xA0000000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x1000</size>
        <usage>registers</usage>
      </addressBlock>
      <addressBlock>
        <offset>0x1000</offset>
        <size>0xFFFFF400</size>
        <usage>reserved</usage>
      </addressBlock>
      <registers>
        <register>
          <name>BCR1</name>
          <displayName>BCR1</displayName>
          <description>SRAM/NOR-Flash chip-select control register
          1</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x000030D0</resetValue>
          <fields>
            <field>
              <name>CBURSTRW</name>
              <description>CBURSTRW</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ASYNCWAIT</name>
              <description>ASYNCWAIT</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EXTMOD</name>
              <description>EXTMOD</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITEN</name>
              <description>WAITEN</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WREN</name>
              <description>WREN</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITCFG</name>
              <description>WAITCFG</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITPOL</name>
              <description>WAITPOL</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BURSTEN</name>
              <description>BURSTEN</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>FACCEN</name>
              <description>FACCEN</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MWID</name>
              <description>MWID</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MTYP</name>
              <description>MTYP</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MUXEN</name>
              <description>MUXEN</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MBKEN</name>
              <description>MBKEN</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BTR1</name>
          <displayName>BTR1</displayName>
          <description>SRAM/NOR-Flash chip-select timing register
          1</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFFFFFFFF</resetValue>
          <fields>
            <field>
              <name>ACCMOD</name>
              <description>ACCMOD</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DATLAT</name>
              <description>DATLAT</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>CLKDIV</name>
              <description>CLKDIV</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>BUSTURN</name>
              <description>BUSTURN</description>
              <bitOffset>16</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DATAST</name>
              <description>DATAST</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ADDHLD</name>
              <description>ADDHLD</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ADDSET</name>
              <description>ADDSET</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BCR2</name>
          <displayName>BCR2</displayName>
          <description>SRAM/NOR-Flash chip-select control register
          2</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x000030D0</resetValue>
          <fields>
            <field>
              <name>CBURSTRW</name>
              <description>CBURSTRW</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ASYNCWAIT</name>
              <description>ASYNCWAIT</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EXTMOD</name>
              <description>EXTMOD</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITEN</name>
              <description>WAITEN</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WREN</name>
              <description>WREN</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITCFG</name>
              <description>WAITCFG</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WRAPMOD</name>
              <description>WRAPMOD</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITPOL</name>
              <description>WAITPOL</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BURSTEN</name>
              <description>BURSTEN</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>FACCEN</name>
              <description>FACCEN</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MWID</name>
              <description>MWID</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MTYP</name>
              <description>MTYP</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MUXEN</name>
              <description>MUXEN</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MBKEN</name>
              <description>MBKEN</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BTR2</name>
          <displayName>BTR2</displayName>
          <description>SRAM/NOR-Flash chip-select timing register
          2</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFFFFFFFF</resetValue>
          <fields>
            <field>
              <name>ACCMOD</name>
              <description>ACCMOD</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DATLAT</name>
              <description>DATLAT</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>CLKDIV</name>
              <description>CLKDIV</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>BUSTURN</name>
              <description>BUSTURN</description>
              <bitOffset>16</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DATAST</name>
              <description>DATAST</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ADDHLD</name>
              <description>ADDHLD</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ADDSET</name>
              <description>ADDSET</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BCR3</name>
          <displayName>BCR3</displayName>
          <description>SRAM/NOR-Flash chip-select control register
          3</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x000030D0</resetValue>
          <fields>
            <field>
              <name>CBURSTRW</name>
              <description>CBURSTRW</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ASYNCWAIT</name>
              <description>ASYNCWAIT</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EXTMOD</name>
              <description>EXTMOD</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITEN</name>
              <description>WAITEN</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WREN</name>
              <description>WREN</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITCFG</name>
              <description>WAITCFG</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WRAPMOD</name>
              <description>WRAPMOD</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITPOL</name>
              <description>WAITPOL</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BURSTEN</name>
              <description>BURSTEN</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>FACCEN</name>
              <description>FACCEN</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MWID</name>
              <description>MWID</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MTYP</name>
              <description>MTYP</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MUXEN</name>
              <description>MUXEN</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MBKEN</name>
              <description>MBKEN</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BTR3</name>
          <displayName>BTR3</displayName>
          <description>SRAM/NOR-Flash chip-select timing register
          3</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFFFFFFFF</resetValue>
          <fields>
            <field>
              <name>ACCMOD</name>
              <description>ACCMOD</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DATLAT</name>
              <description>DATLAT</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>CLKDIV</name>
              <description>CLKDIV</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>BUSTURN</name>
              <description>BUSTURN</description>
              <bitOffset>16</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DATAST</name>
              <description>DATAST</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ADDHLD</name>
              <description>ADDHLD</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ADDSET</name>
              <description>ADDSET</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BCR4</name>
          <displayName>BCR4</displayName>
          <description>SRAM/NOR-Flash chip-select control register
          4</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x000030D0</resetValue>
          <fields>
            <field>
              <name>CBURSTRW</name>
              <description>CBURSTRW</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ASYNCWAIT</name>
              <description>ASYNCWAIT</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EXTMOD</name>
              <description>EXTMOD</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITEN</name>
              <description>WAITEN</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WREN</name>
              <description>WREN</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITCFG</name>
              <description>WAITCFG</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WRAPMOD</name>
              <description>WRAPMOD</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAITPOL</name>
              <description>WAITPOL</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BURSTEN</name>
              <description>BURSTEN</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>FACCEN</name>
              <description>FACCEN</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MWID</name>
              <description>MWID</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MTYP</name>
              <description>MTYP</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MUXEN</name>
              <description>MUXEN</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MBKEN</name>
              <description>MBKEN</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BTR4</name>
          <displayName>BTR4</displayName>
          <description>SRAM/NOR-Flash chip-select timing register
          4</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFFFFFFFF</resetValue>
          <fields>
            <field>
              <name>ACCMOD</name>
              <description>ACCMOD</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DATLAT</name>
              <description>DATLAT</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>CLKDIV</name>
              <description>CLKDIV</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>BUSTURN</name>
              <description>BUSTURN</description>
              <bitOffset>16</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DATAST</name>
              <description>DATAST</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ADDHLD</name>
              <description>ADDHLD</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ADDSET</name>
              <description>ADDSET</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PCR2</name>
          <displayName>PCR2</displayName>
          <description>PC Card/NAND Flash control register
          2</description>
          <addressOffset>0x60</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000018</resetValue>
          <fields>
            <field>
              <name>ECCPS</name>
              <description>ECCPS</description>
              <bitOffset>17</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>TAR</name>
              <description>TAR</description>
              <bitOffset>13</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>TCLR</name>
              <description>TCLR</description>
              <bitOffset>9</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ECCEN</name>
              <description>ECCEN</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PWID</name>
              <description>PWID</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PTYP</name>
              <description>PTYP</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PBKEN</name>
              <description>PBKEN</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PWAITEN</name>
              <description>PWAITEN</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR2</name>
          <displayName>SR2</displayName>
          <description>FIFO status and interrupt register
          2</description>
          <addressOffset>0x64</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000040</resetValue>
          <fields>
            <field>
              <name>FEMPT</name>
              <description>FEMPT</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>IFEN</name>
              <description>IFEN</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ILEN</name>
              <description>ILEN</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IREN</name>
              <description>IREN</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IFS</name>
              <description>IFS</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ILS</name>
              <description>ILS</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IRS</name>
              <description>IRS</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
        <register>
          <name>PMEM2</name>
          <displayName>PMEM2</displayName>
          <description>Common memory space timing register
          2</description>
          <addressOffset>0x68</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFCFCFCFC</resetValue>
          <fields>
            <field>
              <name>MEMHIZx</name>
              <description>MEMHIZx</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMHOLDx</name>
              <description>MEMHOLDx</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMWAITx</name>
              <description>MEMWAITx</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMSETx</name>
              <description>MEMSETx</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PATT2</name>
          <displayName>PATT2</displayName>
          <description>Attribute memory space timing register
          2</description>
          <addressOffset>0x6C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFCFCFCFC</resetValue>
          <fields>
            <field>
              <name>ATTHIZx</name>
              <description>Attribute memory x databus HiZ
              time</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTHOLDx</name>
              <description>Attribute memory x hold
              time</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTWAITx</name>
              <description>Attribute memory x wait
              time</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTSETx</name>
              <description>Attribute memory x setup
              time</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ECCR2</name>
          <displayName>ECCR2</displayName>
          <description>ECC result register 2</description>
          <addressOffset>0x74</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ECCx</name>
              <description>ECC result</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PCR3</name>
          <displayName>PCR3</displayName>
          <description>PC Card/NAND Flash control register
          3</description>
          <addressOffset>0x80</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000018</resetValue>
          <fields>
            <field>
              <name>ECCPS</name>
              <description>ECCPS</description>
              <bitOffset>17</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>TAR</name>
              <description>TAR</description>
              <bitOffset>13</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>TCLR</name>
              <description>TCLR</description>
              <bitOffset>9</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ECCEN</name>
              <description>ECCEN</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PWID</name>
              <description>PWID</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PTYP</name>
              <description>PTYP</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PBKEN</name>
              <description>PBKEN</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PWAITEN</name>
              <description>PWAITEN</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR3</name>
          <displayName>SR3</displayName>
          <description>FIFO status and interrupt register
          3</description>
          <addressOffset>0x84</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000040</resetValue>
          <fields>
            <field>
              <name>FEMPT</name>
              <description>FEMPT</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>IFEN</name>
              <description>IFEN</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ILEN</name>
              <description>ILEN</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IREN</name>
              <description>IREN</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IFS</name>
              <description>IFS</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ILS</name>
              <description>ILS</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IRS</name>
              <description>IRS</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
        <register>
          <name>PMEM3</name>
          <displayName>PMEM3</displayName>
          <description>Common memory space timing register
          3</description>
          <addressOffset>0x88</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFCFCFCFC</resetValue>
          <fields>
            <field>
              <name>MEMHIZx</name>
              <description>MEMHIZx</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMHOLDx</name>
              <description>MEMHOLDx</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMWAITx</name>
              <description>MEMWAITx</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMSETx</name>
              <description>MEMSETx</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PATT3</name>
          <displayName>PATT3</displayName>
          <description>Attribute memory space timing register
          3</description>
          <addressOffset>0x8C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFCFCFCFC</resetValue>
          <fields>
            <field>
              <name>ATTHIZx</name>
              <description>ATTHIZx</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTHOLDx</name>
              <description>ATTHOLDx</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTWAITx</name>
              <description>ATTWAITx</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTSETx</name>
              <description>ATTSETx</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ECCR3</name>
          <displayName>ECCR3</displayName>
          <description>ECC result register 3</description>
          <addressOffset>0x94</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ECCx</name>
              <description>ECCx</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PCR4</name>
          <displayName>PCR4</displayName>
          <description>PC Card/NAND Flash control register
          4</description>
          <addressOffset>0xA0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000018</resetValue>
          <fields>
            <field>
              <name>ECCPS</name>
              <description>ECCPS</description>
              <bitOffset>17</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>TAR</name>
              <description>TAR</description>
              <bitOffset>13</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>TCLR</name>
              <description>TCLR</description>
              <bitOffset>9</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ECCEN</name>
              <description>ECCEN</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PWID</name>
              <description>PWID</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PTYP</name>
              <description>PTYP</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PBKEN</name>
              <description>PBKEN</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PWAITEN</name>
              <description>PWAITEN</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR4</name>
          <displayName>SR4</displayName>
          <description>FIFO status and interrupt register
          4</description>
          <addressOffset>0xA4</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000040</resetValue>
          <fields>
            <field>
              <name>FEMPT</name>
              <description>FEMPT</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>IFEN</name>
              <description>IFEN</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ILEN</name>
              <description>ILEN</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IREN</name>
              <description>IREN</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IFS</name>
              <description>IFS</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ILS</name>
              <description>ILS</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IRS</name>
              <description>IRS</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
        <register>
          <name>PMEM4</name>
          <displayName>PMEM4</displayName>
          <description>Common memory space timing register
          4</description>
          <addressOffset>0xA8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFCFCFCFC</resetValue>
          <fields>
            <field>
              <name>MEMHIZx</name>
              <description>MEMHIZx</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMHOLDx</name>
              <description>MEMHOLDx</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMWAITx</name>
              <description>MEMWAITx</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>MEMSETx</name>
              <description>MEMSETx</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PATT4</name>
          <displayName>PATT4</displayName>
          <description>Attribute memory space timing register
          4</description>
          <addressOffset>0xAC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFCFCFCFC</resetValue>
          <fields>
            <field>
              <name>ATTHIZx</name>
              <description>ATTHIZx</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTHOLDx</name>
              <description>ATTHOLDx</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTWAITx</name>
              <description>ATTWAITx</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ATTSETx</name>
              <description>ATTSETx</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PIO4</name>
          <displayName>PIO4</displayName>
          <description>I/O space timing register 4</description>
          <addressOffset>0xB0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFCFCFCFC</resetValue>
          <fields>
            <field>
              <name>IOHIZx</name>
              <description>IOHIZx</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IOHOLDx</name>
              <description>IOHOLDx</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IOWAITx</name>
              <description>IOWAITx</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IOSETx</name>
              <description>IOSETx</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BWTR1</name>
          <displayName>BWTR1</displayName>
          <description>SRAM/NOR-Flash write timing registers
          1</description>
          <addressOffset>0x104</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0FFFFFFF</resetValue>
          <fields>
            <field>
              <name>ACCMOD</name>
              <description>ACCMOD</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DATLAT</name>
              <description>DATLAT</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>CLKDIV</name>
              <description>CLKDIV</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DATAST</name>
              <description>DATAST</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ADDHLD</name>
              <description>ADDHLD</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ADDSET</name>
              <description>ADDSET</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BWTR2</name>
          <displayName>BWTR2</displayName>
          <description>SRAM/NOR-Flash write timing registers
          2</description>
          <addressOffset>0x10C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0FFFFFFF</resetValue>
          <fields>
            <field>
              <name>ACCMOD</name>
              <description>ACCMOD</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DATLAT</name>
              <description>DATLAT</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>CLKDIV</name>
              <description>CLKDIV</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DATAST</name>
              <description>DATAST</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ADDHLD</name>
              <description>ADDHLD</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ADDSET</name>
              <description>ADDSET</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BWTR3</name>
          <displayName>BWTR3</displayName>
          <description>SRAM/NOR-Flash write timing registers
          3</description>
          <addressOffset>0x114</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0FFFFFFF</resetValue>
          <fields>
            <field>
              <name>ACCMOD</name>
              <description>ACCMOD</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DATLAT</name>
              <description>DATLAT</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>CLKDIV</name>
              <description>CLKDIV</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DATAST</name>
              <description>DATAST</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ADDHLD</name>
              <description>ADDHLD</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ADDSET</name>
              <description>ADDSET</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BWTR4</name>
          <displayName>BWTR4</displayName>
          <description>SRAM/NOR-Flash write timing registers
          4</description>
          <addressOffset>0x11C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0FFFFFFF</resetValue>
          <fields>
            <field>
              <name>ACCMOD</name>
              <description>ACCMOD</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DATLAT</name>
              <description>DATLAT</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>CLKDIV</name>
              <description>CLKDIV</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DATAST</name>
              <description>DATAST</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>ADDHLD</name>
              <description>ADDHLD</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>ADDSET</name>
              <description>ADDSET</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>PWR</name>
      <description>Power control</description>
      <groupName>PWR</groupName>
      <baseAddress>0x40007000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>PVD</name>
        <description>PVD through EXTI Line detection
        interrupt</description>
        <value>1</value>
      </interrupt>
      <registers>
        <register>
          <name>CR</name>
          <displayName>CR</displayName>
          <description>Power control register
          (PWR_CR)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>LPDS</name>
              <description>Low Power Deep Sleep</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PDDS</name>
              <description>Power Down Deep Sleep</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CWUF</name>
              <description>Clear Wake-up Flag</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CSBF</name>
              <description>Clear STANDBY Flag</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PVDE</name>
              <description>Power Voltage Detector
              Enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PLS</name>
              <description>PVD Level Selection</description>
              <bitOffset>5</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>DBP</name>
              <description>Disable Backup Domain write
              protection</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CSR</name>
          <displayName>CSR</displayName>
          <description>Power control register
          (PWR_CR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>WUF</name>
              <description>Wake-Up Flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>SBF</name>
              <description>STANDBY Flag</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>PVDO</name>
              <description>PVD Output</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>EWUP</name>
              <description>Enable WKUP pin</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>RCC</name>
      <description>Reset and clock control</description>
      <groupName>RCC</groupName>
      <baseAddress>0x40021000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>RCC</name>
        <description>RCC global interrupt</description>
        <value>5</value>
      </interrupt>
      <registers>
        <register>
          <name>CR</name>
          <displayName>CR</displayName>
          <description>Clock control register</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000083</resetValue>
          <fields>
            <field>
              <name>HSION</name>
              <description>Internal High Speed clock
              enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>HSIRDY</name>
              <description>Internal High Speed clock ready
              flag</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>HSITRIM</name>
              <description>Internal High Speed clock
              trimming</description>
              <bitOffset>3</bitOffset>
              <bitWidth>5</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>HSICAL</name>
              <description>Internal High Speed clock
              Calibration</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>HSEON</name>
              <description>External High Speed clock
              enable</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>HSERDY</name>
              <description>External High Speed clock ready
              flag</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>HSEBYP</name>
              <description>External High Speed clock
              Bypass</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>CSSON</name>
              <description>Clock Security System
              enable</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PLLON</name>
              <description>PLL enable</description>
              <bitOffset>24</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PLLRDY</name>
              <description>PLL clock ready flag</description>
              <bitOffset>25</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>CFGR</name>
          <displayName>CFGR</displayName>
          <description>Clock configuration register
          (RCC_CFGR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SW</name>
              <description>System clock Switch</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>SWS</name>
              <description>System Clock Switch Status</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>HPRE</name>
              <description>AHB prescaler</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PPRE1</name>
              <description>APB Low speed prescaler
              (APB1)</description>
              <bitOffset>8</bitOffset>
              <bitWidth>3</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PPRE2</name>
              <description>APB High speed prescaler
              (APB2)</description>
              <bitOffset>11</bitOffset>
              <bitWidth>3</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ADCPRE</name>
              <description>ADC prescaler</description>
              <bitOffset>14</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PLLSRC</name>
              <description>PLL entry clock source</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PLLXTPRE</name>
              <description>HSE divider for PLL entry</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PLLMUL</name>
              <description>PLL Multiplication Factor</description>
              <bitOffset>18</bitOffset>
              <bitWidth>4</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>MCO</name>
              <description>Microcontroller clock
              output</description>
              <bitOffset>24</bitOffset>
              <bitWidth>3</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
        <register>
          <name>CIR</name>
          <displayName>CIR</displayName>
          <description>Clock interrupt register
          (RCC_CIR)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>LSIRDYF</name>
              <description>LSI Ready Interrupt flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>LSERDYF</name>
              <description>LSE Ready Interrupt flag</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>HSIRDYF</name>
              <description>HSI Ready Interrupt flag</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>HSERDYF</name>
              <description>HSE Ready Interrupt flag</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>PLLRDYF</name>
              <description>PLL Ready Interrupt flag</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>CSSF</name>
              <description>Clock Security System Interrupt
              flag</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>LSIRDYIE</name>
              <description>LSI Ready Interrupt Enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>LSERDYIE</name>
              <description>LSE Ready Interrupt Enable</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>HSIRDYIE</name>
              <description>HSI Ready Interrupt Enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>HSERDYIE</name>
              <description>HSE Ready Interrupt Enable</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PLLRDYIE</name>
              <description>PLL Ready Interrupt Enable</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>LSIRDYC</name>
              <description>LSI Ready Interrupt Clear</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
              <access>write-only</access>
            </field>
            <field>
              <name>LSERDYC</name>
              <description>LSE Ready Interrupt Clear</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
              <access>write-only</access>
            </field>
            <field>
              <name>HSIRDYC</name>
              <description>HSI Ready Interrupt Clear</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
              <access>write-only</access>
            </field>
            <field>
              <name>HSERDYC</name>
              <description>HSE Ready Interrupt Clear</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
              <access>write-only</access>
            </field>
            <field>
              <name>PLLRDYC</name>
              <description>PLL Ready Interrupt Clear</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
              <access>write-only</access>
            </field>
            <field>
              <name>CSSC</name>
              <description>Clock security system interrupt
              clear</description>
              <bitOffset>23</bitOffset>
              <bitWidth>1</bitWidth>
              <access>write-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>APB2RSTR</name>
          <displayName>APB2RSTR</displayName>
          <description>APB2 peripheral reset register
          (RCC_APB2RSTR)</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x000000000</resetValue>
          <fields>
            <field>
              <name>AFIORST</name>
              <description>Alternate function I/O
              reset</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPARST</name>
              <description>IO port A reset</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPBRST</name>
              <description>IO port B reset</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPCRST</name>
              <description>IO port C reset</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPDRST</name>
              <description>IO port D reset</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPERST</name>
              <description>IO port E reset</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ADC1RST</name>
              <description>ADC 1 interface reset</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ADC2RST</name>
              <description>ADC 2 interface reset</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SPI1RST</name>
              <description>SPI 1 reset</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>USART1RST</name>
              <description>USART1 reset</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM9RST</name>
              <description>TIM9 timer reset</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM10RST</name>
              <description>TIM10 timer reset</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM11RST</name>
              <description>TIM11 timer reset</description>
              <bitOffset>21</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPFRST</name>
              <description>IO port F reset</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPGRST</name>
              <description>IO port G reset</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>APB1RSTR</name>
          <displayName>APB1RSTR</displayName>
          <description>APB1 peripheral reset register
          (RCC_APB1RSTR)</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>TIM2RST</name>
              <description>Timer 2 reset</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM3RST</name>
              <description>Timer 3 reset</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM4RST</name>
              <description>Timer 4 reset</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM5RST</name>
              <description>Timer 5 reset</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM6RST</name>
              <description>Timer 6 reset</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM7RST</name>
              <description>Timer 7 reset</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM12RST</name>
              <description>Timer 12 reset</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM13RST</name>
              <description>Timer 13 reset</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM14RST</name>
              <description>Timer 14 reset</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WWDGRST</name>
              <description>Window watchdog reset</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SPI2RST</name>
              <description>SPI2 reset</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SPI3RST</name>
              <description>SPI3 reset</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>USART2RST</name>
              <description>USART 2 reset</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>USART3RST</name>
              <description>USART 3 reset</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UART4RST</name>
              <description>UART 4 reset</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UART5RST</name>
              <description>UART 5 reset</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>I2C1RST</name>
              <description>I2C1 reset</description>
              <bitOffset>21</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>I2C2RST</name>
              <description>I2C2 reset</description>
              <bitOffset>22</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BKPRST</name>
              <description>Backup interface reset</description>
              <bitOffset>27</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PWRRST</name>
              <description>Power interface reset</description>
              <bitOffset>28</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DACRST</name>
              <description>DAC interface reset</description>
              <bitOffset>29</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>AHBENR</name>
          <displayName>AHBENR</displayName>
          <description>AHB Peripheral Clock enable register
          (RCC_AHBENR)</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000014</resetValue>
          <fields>
            <field>
              <name>DMA1EN</name>
              <description>DMA1 clock enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DMA2EN</name>
              <description>DMA2 clock enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SRAMEN</name>
              <description>SRAM interface clock
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>FLITFEN</name>
              <description>FLITF clock enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CRCEN</name>
              <description>CRC clock enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>FSMCEN</name>
              <description>FSMC clock enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>APB2ENR</name>
          <displayName>APB2ENR</displayName>
          <description>APB2 peripheral clock enable register
          (RCC_APB2ENR)</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>AFIOEN</name>
              <description>Alternate function I/O clock
              enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPAEN</name>
              <description>I/O port A clock enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPBEN</name>
              <description>I/O port B clock enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPCEN</name>
              <description>I/O port C clock enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPDEN</name>
              <description>I/O port D clock enable</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPEEN</name>
              <description>I/O port E clock enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPFEN</name>
              <description>I/O port F clock enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IOPGEN</name>
              <description>I/O port G clock enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ADC1EN</name>
              <description>ADC 1 interface clock
              enable</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SPI1EN</name>
              <description>SPI 1 clock enable</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>USART1EN</name>
              <description>USART1 clock enable</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM9EN</name>
              <description>TIM9 Timer clock enable</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM10EN</name>
              <description>TIM10 Timer clock enable</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM11EN</name>
              <description>TIM11 Timer clock enable</description>
              <bitOffset>21</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>APB1ENR</name>
          <displayName>APB1ENR</displayName>
          <description>APB1 peripheral clock enable register
          (RCC_APB1ENR)</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>TIM2EN</name>
              <description>Timer 2 clock enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM3EN</name>
              <description>Timer 3 clock enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM4EN</name>
              <description>Timer 4 clock enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM5EN</name>
              <description>Timer 5 clock enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM6EN</name>
              <description>Timer 6 clock enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM7EN</name>
              <description>Timer 7 clock enable</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM12EN</name>
              <description>Timer 12 clock enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM13EN</name>
              <description>Timer 13 clock enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM14EN</name>
              <description>Timer 14 clock enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WWDGEN</name>
              <description>Window watchdog clock
              enable</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SPI2EN</name>
              <description>SPI 2 clock enable</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SPI3EN</name>
              <description>SPI 3 clock enable</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>USART2EN</name>
              <description>USART 2 clock enable</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>USART3EN</name>
              <description>USART 3 clock enable</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UART4EN</name>
              <description>UART 4 clock enable</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UART5EN</name>
              <description>UART 5 clock enable</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>I2C1EN</name>
              <description>I2C 1 clock enable</description>
              <bitOffset>21</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>I2C2EN</name>
              <description>I2C 2 clock enable</description>
              <bitOffset>22</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BKPEN</name>
              <description>Backup interface clock
              enable</description>
              <bitOffset>27</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PWREN</name>
              <description>Power interface clock
              enable</description>
              <bitOffset>28</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DACEN</name>
              <description>DAC interface clock enable</description>
              <bitOffset>29</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BDCR</name>
          <displayName>BDCR</displayName>
          <description>Backup domain control register
          (RCC_BDCR)</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>LSEON</name>
              <description>External Low Speed oscillator
              enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>LSERDY</name>
              <description>External Low Speed oscillator
              ready</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>LSEBYP</name>
              <description>External Low Speed oscillator
              bypass</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>RTCSEL</name>
              <description>RTC clock source selection</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>RTCEN</name>
              <description>RTC clock enable</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>BDRST</name>
              <description>Backup domain software
              reset</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
        <register>
          <name>CSR</name>
          <displayName>CSR</displayName>
          <description>Control/status register
          (RCC_CSR)</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <resetValue>0x0C000000</resetValue>
          <fields>
            <field>
              <name>LSION</name>
              <description>Internal low speed oscillator
              enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>LSIRDY</name>
              <description>Internal low speed oscillator
              ready</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>RMVF</name>
              <description>Remove reset flag</description>
              <bitOffset>24</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PINRSTF</name>
              <description>PIN reset flag</description>
              <bitOffset>26</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PORRSTF</name>
              <description>POR/PDR reset flag</description>
              <bitOffset>27</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>SFTRSTF</name>
              <description>Software reset flag</description>
              <bitOffset>28</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IWDGRSTF</name>
              <description>Independent watchdog reset
              flag</description>
              <bitOffset>29</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>WWDGRSTF</name>
              <description>Window watchdog reset flag</description>
              <bitOffset>30</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>LPWRRSTF</name>
              <description>Low-power reset flag</description>
              <bitOffset>31</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>GPIOA</name>
      <description>General purpose I/O</description>
      <groupName>GPIO</groupName>
      <baseAddress>0x40010800</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>CRL</name>
          <displayName>CRL</displayName>
          <description>Port configuration register low
          (GPIOn_CRL)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x44444444</resetValue>
          <fields>
            <field>
              <name>MODE0</name>
              <description>Port n.0 mode bits</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF0</name>
              <description>Port n.0 configuration
              bits</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE1</name>
              <description>Port n.1 mode bits</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF1</name>
              <description>Port n.1 configuration
              bits</description>
              <bitOffset>6</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE2</name>
              <description>Port n.2 mode bits</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF2</name>
              <description>Port n.2 configuration
              bits</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE3</name>
              <description>Port n.3 mode bits</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF3</name>
              <description>Port n.3 configuration
              bits</description>
              <bitOffset>14</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE4</name>
              <description>Port n.4 mode bits</description>
              <bitOffset>16</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF4</name>
              <description>Port n.4 configuration
              bits</description>
              <bitOffset>18</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE5</name>
              <description>Port n.5 mode bits</description>
              <bitOffset>20</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF5</name>
              <description>Port n.5 configuration
              bits</description>
              <bitOffset>22</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE6</name>
              <description>Port n.6 mode bits</description>
              <bitOffset>24</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF6</name>
              <description>Port n.6 configuration
              bits</description>
              <bitOffset>26</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE7</name>
              <description>Port n.7 mode bits</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF7</name>
              <description>Port n.7 configuration
              bits</description>
              <bitOffset>30</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CRH</name>
          <displayName>CRH</displayName>
          <description>Port configuration register high
          (GPIOn_CRL)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x44444444</resetValue>
          <fields>
            <field>
              <name>MODE8</name>
              <description>Port n.8 mode bits</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF8</name>
              <description>Port n.8 configuration
              bits</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE9</name>
              <description>Port n.9 mode bits</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF9</name>
              <description>Port n.9 configuration
              bits</description>
              <bitOffset>6</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE10</name>
              <description>Port n.10 mode bits</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF10</name>
              <description>Port n.10 configuration
              bits</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE11</name>
              <description>Port n.11 mode bits</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF11</name>
              <description>Port n.11 configuration
              bits</description>
              <bitOffset>14</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE12</name>
              <description>Port n.12 mode bits</description>
              <bitOffset>16</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF12</name>
              <description>Port n.12 configuration
              bits</description>
              <bitOffset>18</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE13</name>
              <description>Port n.13 mode bits</description>
              <bitOffset>20</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF13</name>
              <description>Port n.13 configuration
              bits</description>
              <bitOffset>22</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE14</name>
              <description>Port n.14 mode bits</description>
              <bitOffset>24</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF14</name>
              <description>Port n.14 configuration
              bits</description>
              <bitOffset>26</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MODE15</name>
              <description>Port n.15 mode bits</description>
              <bitOffset>28</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CNF15</name>
              <description>Port n.15 configuration
              bits</description>
              <bitOffset>30</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IDR</name>
          <displayName>IDR</displayName>
          <description>Port input data register
          (GPIOn_IDR)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IDR0</name>
              <description>Port input data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR1</name>
              <description>Port input data</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR2</name>
              <description>Port input data</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR3</name>
              <description>Port input data</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR4</name>
              <description>Port input data</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR5</name>
              <description>Port input data</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR6</name>
              <description>Port input data</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR7</name>
              <description>Port input data</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR8</name>
              <description>Port input data</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR9</name>
              <description>Port input data</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR10</name>
              <description>Port input data</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR11</name>
              <description>Port input data</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR12</name>
              <description>Port input data</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR13</name>
              <description>Port input data</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR14</name>
              <description>Port input data</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDR15</name>
              <description>Port input data</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ODR</name>
          <displayName>ODR</displayName>
          <description>Port output data register
          (GPIOn_ODR)</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ODR0</name>
              <description>Port output data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR1</name>
              <description>Port output data</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR2</name>
              <description>Port output data</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR3</name>
              <description>Port output data</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR4</name>
              <description>Port output data</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR5</name>
              <description>Port output data</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR6</name>
              <description>Port output data</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR7</name>
              <description>Port output data</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR8</name>
              <description>Port output data</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR9</name>
              <description>Port output data</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR10</name>
              <description>Port output data</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR11</name>
              <description>Port output data</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR12</name>
              <description>Port output data</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR13</name>
              <description>Port output data</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR14</name>
              <description>Port output data</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODR15</name>
              <description>Port output data</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BSRR</name>
          <displayName>BSRR</displayName>
          <description>Port bit set/reset register
          (GPIOn_BSRR)</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>BS0</name>
              <description>Set bit 0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS1</name>
              <description>Set bit 1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS2</name>
              <description>Set bit 1</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS3</name>
              <description>Set bit 3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS4</name>
              <description>Set bit 4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS5</name>
              <description>Set bit 5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS6</name>
              <description>Set bit 6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS7</name>
              <description>Set bit 7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS8</name>
              <description>Set bit 8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS9</name>
              <description>Set bit 9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS10</name>
              <description>Set bit 10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS11</name>
              <description>Set bit 11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS12</name>
              <description>Set bit 12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS13</name>
              <description>Set bit 13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS14</name>
              <description>Set bit 14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BS15</name>
              <description>Set bit 15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR0</name>
              <description>Reset bit 0</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR1</name>
              <description>Reset bit 1</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR2</name>
              <description>Reset bit 2</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR3</name>
              <description>Reset bit 3</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR4</name>
              <description>Reset bit 4</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR5</name>
              <description>Reset bit 5</description>
              <bitOffset>21</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR6</name>
              <description>Reset bit 6</description>
              <bitOffset>22</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR7</name>
              <description>Reset bit 7</description>
              <bitOffset>23</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR8</name>
              <description>Reset bit 8</description>
              <bitOffset>24</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR9</name>
              <description>Reset bit 9</description>
              <bitOffset>25</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR10</name>
              <description>Reset bit 10</description>
              <bitOffset>26</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR11</name>
              <description>Reset bit 11</description>
              <bitOffset>27</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR12</name>
              <description>Reset bit 12</description>
              <bitOffset>28</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR13</name>
              <description>Reset bit 13</description>
              <bitOffset>29</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR14</name>
              <description>Reset bit 14</description>
              <bitOffset>30</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR15</name>
              <description>Reset bit 15</description>
              <bitOffset>31</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BRR</name>
          <displayName>BRR</displayName>
          <description>Port bit reset register
          (GPIOn_BRR)</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>BR0</name>
              <description>Reset bit 0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR1</name>
              <description>Reset bit 1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR2</name>
              <description>Reset bit 1</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR3</name>
              <description>Reset bit 3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR4</name>
              <description>Reset bit 4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR5</name>
              <description>Reset bit 5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR6</name>
              <description>Reset bit 6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR7</name>
              <description>Reset bit 7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR8</name>
              <description>Reset bit 8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR9</name>
              <description>Reset bit 9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR10</name>
              <description>Reset bit 10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR11</name>
              <description>Reset bit 11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR12</name>
              <description>Reset bit 12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR13</name>
              <description>Reset bit 13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR14</name>
              <description>Reset bit 14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR15</name>
              <description>Reset bit 15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>LCKR</name>
          <displayName>LCKR</displayName>
          <description>Port configuration lock
          register</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>LCK0</name>
              <description>Port A Lock bit 0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK1</name>
              <description>Port A Lock bit 1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK2</name>
              <description>Port A Lock bit 2</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK3</name>
              <description>Port A Lock bit 3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK4</name>
              <description>Port A Lock bit 4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK5</name>
              <description>Port A Lock bit 5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK6</name>
              <description>Port A Lock bit 6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK7</name>
              <description>Port A Lock bit 7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK8</name>
              <description>Port A Lock bit 8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK9</name>
              <description>Port A Lock bit 9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK10</name>
              <description>Port A Lock bit 10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK11</name>
              <description>Port A Lock bit 11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK12</name>
              <description>Port A Lock bit 12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK13</name>
              <description>Port A Lock bit 13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK14</name>
              <description>Port A Lock bit 14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCK15</name>
              <description>Port A Lock bit 15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LCKK</name>
              <description>Lock key</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="GPIOA">
      <name>GPIOB</name>
      <baseAddress>0x40010C00</baseAddress>
    </peripheral>
    <peripheral derivedFrom="GPIOA">
      <name>GPIOC</name>
      <baseAddress>0x40011000</baseAddress>
    </peripheral>
    <peripheral derivedFrom="GPIOA">
      <name>GPIOD</name>
      <baseAddress>0x40011400</baseAddress>
    </peripheral>
    <peripheral derivedFrom="GPIOA">
      <name>GPIOE</name>
      <baseAddress>0x40011800</baseAddress>
    </peripheral>
    <peripheral derivedFrom="GPIOA">
      <name>GPIOF</name>
      <baseAddress>0x40011C00</baseAddress>
    </peripheral>
    <peripheral derivedFrom="GPIOA">
      <name>GPIOG</name>
      <baseAddress>0x40012000</baseAddress>
    </peripheral>
    <peripheral>
      <name>AFIO</name>
      <description>Alternate function I/O</description>
      <groupName>AFIO</groupName>
      <baseAddress>0x40010000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>EVCR</name>
          <displayName>EVCR</displayName>
          <description>Event Control Register
          (AFIO_EVCR)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PIN</name>
              <description>Pin selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>PORT</name>
              <description>Port selection</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>EVOE</name>
              <description>Event Output Enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>MAPR</name>
          <displayName>MAPR</displayName>
          <description>AF remap and debug I/O configuration
          register (AFIO_MAPR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SPI1_REMAP</name>
              <description>SPI1 remapping</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>I2C1_REMAP</name>
              <description>I2C1 remapping</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>USART1_REMAP</name>
              <description>USART1 remapping</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>USART2_REMAP</name>
              <description>USART2 remapping</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>USART3_REMAP</name>
              <description>USART3 remapping</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TIM1_REMAP</name>
              <description>TIM1 remapping</description>
              <bitOffset>6</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TIM2_REMAP</name>
              <description>TIM2 remapping</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TIM3_REMAP</name>
              <description>TIM3 remapping</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TIM4_REMAP</name>
              <description>TIM4 remapping</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>CAN_REMAP</name>
              <description>CAN1 remapping</description>
              <bitOffset>13</bitOffset>
              <bitWidth>2</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PD01_REMAP</name>
              <description>Port D0/Port D1 mapping on
              OSCIN/OSCOUT</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TIM5CH4_IREMAP</name>
              <description>Set and cleared by
              software</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ADC1_ETRGINJ_REMAP</name>
              <description>ADC 1 External trigger injected
              conversion remapping</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ADC1_ETRGREG_REMAP</name>
              <description>ADC 1 external trigger regular
              conversion remapping</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ADC2_ETRGINJ_REMAP</name>
              <description>ADC 2 external trigger injected
              conversion remapping</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ADC2_ETRGREG_REMAP</name>
              <description>ADC 2 external trigger regular
              conversion remapping</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>SWJ_CFG</name>
              <description>Serial wire JTAG
              configuration</description>
              <bitOffset>24</bitOffset>
              <bitWidth>3</bitWidth>
              <access>write-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>EXTICR1</name>
          <displayName>EXTICR1</displayName>
          <description>External interrupt configuration register 1
          (AFIO_EXTICR1)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EXTI0</name>
              <description>EXTI0 configuration</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI1</name>
              <description>EXTI1 configuration</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI2</name>
              <description>EXTI2 configuration</description>
              <bitOffset>8</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI3</name>
              <description>EXTI3 configuration</description>
              <bitOffset>12</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>EXTICR2</name>
          <displayName>EXTICR2</displayName>
          <description>External interrupt configuration register 2
          (AFIO_EXTICR2)</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EXTI4</name>
              <description>EXTI4 configuration</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI5</name>
              <description>EXTI5 configuration</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI6</name>
              <description>EXTI6 configuration</description>
              <bitOffset>8</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI7</name>
              <description>EXTI7 configuration</description>
              <bitOffset>12</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>EXTICR3</name>
          <displayName>EXTICR3</displayName>
          <description>External interrupt configuration register 3
          (AFIO_EXTICR3)</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EXTI8</name>
              <description>EXTI8 configuration</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI9</name>
              <description>EXTI9 configuration</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI10</name>
              <description>EXTI10 configuration</description>
              <bitOffset>8</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI11</name>
              <description>EXTI11 configuration</description>
              <bitOffset>12</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>EXTICR4</name>
          <displayName>EXTICR4</displayName>
          <description>External interrupt configuration register 4
          (AFIO_EXTICR4)</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EXTI12</name>
              <description>EXTI12 configuration</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI13</name>
              <description>EXTI13 configuration</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI14</name>
              <description>EXTI14 configuration</description>
              <bitOffset>8</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>EXTI15</name>
              <description>EXTI15 configuration</description>
              <bitOffset>12</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>MAPR2</name>
          <displayName>MAPR2</displayName>
          <description>AF remap and debug I/O configuration
          register</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>TIM9_REMAP</name>
              <description>TIM9 remapping</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM10_REMAP</name>
              <description>TIM10 remapping</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM11_REMAP</name>
              <description>TIM11 remapping</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM13_REMAP</name>
              <description>TIM13 remapping</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIM14_REMAP</name>
              <description>TIM14 remapping</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>FSMC_NADV</name>
              <description>NADV connect/disconnect</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>EXTI</name>
      <description>EXTI</description>
      <groupName>EXTI</groupName>
      <baseAddress>0x40010400</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>TAMPER_STAMP</name>
        <description>Tamper and TimeStamp through EXTI line
        interrupts</description>
        <value>2</value>
      </interrupt>
      <interrupt>
        <name>EXTI0</name>
        <description>EXTI Line0 interrupt</description>
        <value>6</value>
      </interrupt>
      <interrupt>
        <name>EXTI1</name>
        <description>EXTI Line1 interrupt</description>
        <value>7</value>
      </interrupt>
      <interrupt>
        <name>EXTI2</name>
        <description>EXTI Line2 interrupt</description>
        <value>8</value>
      </interrupt>
      <interrupt>
        <name>EXTI3</name>
        <description>EXTI Line3 interrupt</description>
        <value>9</value>
      </interrupt>
      <interrupt>
        <name>EXTI4</name>
        <description>EXTI Line4 interrupt</description>
        <value>10</value>
      </interrupt>
      <interrupt>
        <name>COMP_CA</name>
        <description>Comparator wakeup through EXTI line (21 and
        22) interrupt/Channel acquisition interrupt</description>
        <value>22</value>
      </interrupt>
      <interrupt>
        <name>EXTI9_5</name>
        <description>EXTI Line[9:5] interrupts</description>
        <value>23</value>
      </interrupt>
      <interrupt>
        <name>EXTI15_10</name>
        <description>EXTI Line[15:10] interrupts</description>
        <value>40</value>
      </interrupt>
      <registers>
        <register>
          <name>IMR</name>
          <displayName>IMR</displayName>
          <description>Interrupt mask register
          (EXTI_IMR)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MR0</name>
              <description>Interrupt Mask on line 0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR1</name>
              <description>Interrupt Mask on line 1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR2</name>
              <description>Interrupt Mask on line 2</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR3</name>
              <description>Interrupt Mask on line 3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR4</name>
              <description>Interrupt Mask on line 4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR5</name>
              <description>Interrupt Mask on line 5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR6</name>
              <description>Interrupt Mask on line 6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR7</name>
              <description>Interrupt Mask on line 7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR8</name>
              <description>Interrupt Mask on line 8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR9</name>
              <description>Interrupt Mask on line 9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR10</name>
              <description>Interrupt Mask on line 10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR11</name>
              <description>Interrupt Mask on line 11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR12</name>
              <description>Interrupt Mask on line 12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR13</name>
              <description>Interrupt Mask on line 13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR14</name>
              <description>Interrupt Mask on line 14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR15</name>
              <description>Interrupt Mask on line 15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR16</name>
              <description>Interrupt Mask on line 16</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR17</name>
              <description>Interrupt Mask on line 17</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR18</name>
              <description>Interrupt Mask on line 18</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>EMR</name>
          <displayName>EMR</displayName>
          <description>Event mask register (EXTI_EMR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MR0</name>
              <description>Event Mask on line 0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR1</name>
              <description>Event Mask on line 1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR2</name>
              <description>Event Mask on line 2</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR3</name>
              <description>Event Mask on line 3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR4</name>
              <description>Event Mask on line 4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR5</name>
              <description>Event Mask on line 5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR6</name>
              <description>Event Mask on line 6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR7</name>
              <description>Event Mask on line 7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR8</name>
              <description>Event Mask on line 8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR9</name>
              <description>Event Mask on line 9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR10</name>
              <description>Event Mask on line 10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR11</name>
              <description>Event Mask on line 11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR12</name>
              <description>Event Mask on line 12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR13</name>
              <description>Event Mask on line 13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR14</name>
              <description>Event Mask on line 14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR15</name>
              <description>Event Mask on line 15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR16</name>
              <description>Event Mask on line 16</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR17</name>
              <description>Event Mask on line 17</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MR18</name>
              <description>Event Mask on line 18</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>RTSR</name>
          <displayName>RTSR</displayName>
          <description>Rising Trigger selection register
          (EXTI_RTSR)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>TR0</name>
              <description>Rising trigger event configuration of
              line 0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR1</name>
              <description>Rising trigger event configuration of
              line 1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR2</name>
              <description>Rising trigger event configuration of
              line 2</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR3</name>
              <description>Rising trigger event configuration of
              line 3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR4</name>
              <description>Rising trigger event configuration of
              line 4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR5</name>
              <description>Rising trigger event configuration of
              line 5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR6</name>
              <description>Rising trigger event configuration of
              line 6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR7</name>
              <description>Rising trigger event configuration of
              line 7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR8</name>
              <description>Rising trigger event configuration of
              line 8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR9</name>
              <description>Rising trigger event configuration of
              line 9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR10</name>
              <description>Rising trigger event configuration of
              line 10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR11</name>
              <description>Rising trigger event configuration of
              line 11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR12</name>
              <description>Rising trigger event configuration of
              line 12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR13</name>
              <description>Rising trigger event configuration of
              line 13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR14</name>
              <description>Rising trigger event configuration of
              line 14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR15</name>
              <description>Rising trigger event configuration of
              line 15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR16</name>
              <description>Rising trigger event configuration of
              line 16</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR17</name>
              <description>Rising trigger event configuration of
              line 17</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR18</name>
              <description>Rising trigger event configuration of
              line 18</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>FTSR</name>
          <displayName>FTSR</displayName>
          <description>Falling Trigger selection register
          (EXTI_FTSR)</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>TR0</name>
              <description>Falling trigger event configuration of
              line 0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR1</name>
              <description>Falling trigger event configuration of
              line 1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR2</name>
              <description>Falling trigger event configuration of
              line 2</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR3</name>
              <description>Falling trigger event configuration of
              line 3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR4</name>
              <description>Falling trigger event configuration of
              line 4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR5</name>
              <description>Falling trigger event configuration of
              line 5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR6</name>
              <description>Falling trigger event configuration of
              line 6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR7</name>
              <description>Falling trigger event configuration of
              line 7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR8</name>
              <description>Falling trigger event configuration of
              line 8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR9</name>
              <description>Falling trigger event configuration of
              line 9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR10</name>
              <description>Falling trigger event configuration of
              line 10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR11</name>
              <description>Falling trigger event configuration of
              line 11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR12</name>
              <description>Falling trigger event configuration of
              line 12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR13</name>
              <description>Falling trigger event configuration of
              line 13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR14</name>
              <description>Falling trigger event configuration of
              line 14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR15</name>
              <description>Falling trigger event configuration of
              line 15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR16</name>
              <description>Falling trigger event configuration of
              line 16</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR17</name>
              <description>Falling trigger event configuration of
              line 17</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TR18</name>
              <description>Falling trigger event configuration of
              line 18</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SWIER</name>
          <displayName>SWIER</displayName>
          <description>Software interrupt event register
          (EXTI_SWIER)</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SWIER0</name>
              <description>Software Interrupt on line
              0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER1</name>
              <description>Software Interrupt on line
              1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER2</name>
              <description>Software Interrupt on line
              2</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER3</name>
              <description>Software Interrupt on line
              3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER4</name>
              <description>Software Interrupt on line
              4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER5</name>
              <description>Software Interrupt on line
              5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER6</name>
              <description>Software Interrupt on line
              6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER7</name>
              <description>Software Interrupt on line
              7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER8</name>
              <description>Software Interrupt on line
              8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER9</name>
              <description>Software Interrupt on line
              9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER10</name>
              <description>Software Interrupt on line
              10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER11</name>
              <description>Software Interrupt on line
              11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER12</name>
              <description>Software Interrupt on line
              12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER13</name>
              <description>Software Interrupt on line
              13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER14</name>
              <description>Software Interrupt on line
              14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER15</name>
              <description>Software Interrupt on line
              15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER16</name>
              <description>Software Interrupt on line
              16</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER17</name>
              <description>Software Interrupt on line
              17</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWIER18</name>
              <description>Software Interrupt on line
              18</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PR</name>
          <displayName>PR</displayName>
          <description>Pending register (EXTI_PR)</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PR0</name>
              <description>Pending bit 0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR1</name>
              <description>Pending bit 1</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR2</name>
              <description>Pending bit 2</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR3</name>
              <description>Pending bit 3</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR4</name>
              <description>Pending bit 4</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR5</name>
              <description>Pending bit 5</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR6</name>
              <description>Pending bit 6</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR7</name>
              <description>Pending bit 7</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR8</name>
              <description>Pending bit 8</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR9</name>
              <description>Pending bit 9</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR10</name>
              <description>Pending bit 10</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR11</name>
              <description>Pending bit 11</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR12</name>
              <description>Pending bit 12</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR13</name>
              <description>Pending bit 13</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR14</name>
              <description>Pending bit 14</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR15</name>
              <description>Pending bit 15</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR16</name>
              <description>Pending bit 16</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR17</name>
              <description>Pending bit 17</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PR18</name>
              <description>Pending bit 18</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>DMA1</name>
      <description>DMA controller</description>
      <groupName>DMA</groupName>
      <baseAddress>0x40020000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>DMA1_Channel1</name>
        <description>DMA1 Channel1 global interrupt</description>
        <value>11</value>
      </interrupt>
      <interrupt>
        <name>DMA1_Channel2</name>
        <description>DMA1 Channel2 global interrupt</description>
        <value>12</value>
      </interrupt>
      <interrupt>
        <name>DMA1_Channel3</name>
        <description>DMA1 Channel3 global interrupt</description>
        <value>13</value>
      </interrupt>
      <interrupt>
        <name>DMA1_Channel4</name>
        <description>DMA1 Channel4 global interrupt</description>
        <value>14</value>
      </interrupt>
      <interrupt>
        <name>DMA1_Channel5</name>
        <description>DMA1 Channel5 global interrupt</description>
        <value>15</value>
      </interrupt>
      <interrupt>
        <name>DMA1_Channel6</name>
        <description>DMA1 Channel6 global interrupt</description>
        <value>16</value>
      </interrupt>
      <interrupt>
        <name>DMA1_Channel7</name>
        <description>DMA1 Channel7 global interrupt</description>
        <value>17</value>
      </interrupt>
      <registers>
        <register>
          <name>ISR</name>
          <displayName>ISR</displayName>
          <description>DMA interrupt status register
          (DMA_ISR)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>GIF1</name>
              <description>Channel 1 Global interrupt
              flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIF1</name>
              <description>Channel 1 Transfer Complete
              flag</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIF1</name>
              <description>Channel 1 Half Transfer Complete
              flag</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIF1</name>
              <description>Channel 1 Transfer Error
              flag</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>GIF2</name>
              <description>Channel 2 Global interrupt
              flag</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIF2</name>
              <description>Channel 2 Transfer Complete
              flag</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIF2</name>
              <description>Channel 2 Half Transfer Complete
              flag</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIF2</name>
              <description>Channel 2 Transfer Error
              flag</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>GIF3</name>
              <description>Channel 3 Global interrupt
              flag</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIF3</name>
              <description>Channel 3 Transfer Complete
              flag</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIF3</name>
              <description>Channel 3 Half Transfer Complete
              flag</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIF3</name>
              <description>Channel 3 Transfer Error
              flag</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>GIF4</name>
              <description>Channel 4 Global interrupt
              flag</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIF4</name>
              <description>Channel 4 Transfer Complete
              flag</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIF4</name>
              <description>Channel 4 Half Transfer Complete
              flag</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIF4</name>
              <description>Channel 4 Transfer Error
              flag</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>GIF5</name>
              <description>Channel 5 Global interrupt
              flag</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIF5</name>
              <description>Channel 5 Transfer Complete
              flag</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIF5</name>
              <description>Channel 5 Half Transfer Complete
              flag</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIF5</name>
              <description>Channel 5 Transfer Error
              flag</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>GIF6</name>
              <description>Channel 6 Global interrupt
              flag</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIF6</name>
              <description>Channel 6 Transfer Complete
              flag</description>
              <bitOffset>21</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIF6</name>
              <description>Channel 6 Half Transfer Complete
              flag</description>
              <bitOffset>22</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIF6</name>
              <description>Channel 6 Transfer Error
              flag</description>
              <bitOffset>23</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>GIF7</name>
              <description>Channel 7 Global interrupt
              flag</description>
              <bitOffset>24</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIF7</name>
              <description>Channel 7 Transfer Complete
              flag</description>
              <bitOffset>25</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIF7</name>
              <description>Channel 7 Half Transfer Complete
              flag</description>
              <bitOffset>26</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIF7</name>
              <description>Channel 7 Transfer Error
              flag</description>
              <bitOffset>27</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IFCR</name>
          <displayName>IFCR</displayName>
          <description>DMA interrupt flag clear register
          (DMA_IFCR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CGIF1</name>
              <description>Channel 1 Global interrupt
              clear</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CGIF2</name>
              <description>Channel 2 Global interrupt
              clear</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CGIF3</name>
              <description>Channel 3 Global interrupt
              clear</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CGIF4</name>
              <description>Channel 4 Global interrupt
              clear</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CGIF5</name>
              <description>Channel 5 Global interrupt
              clear</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CGIF6</name>
              <description>Channel 6 Global interrupt
              clear</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CGIF7</name>
              <description>Channel 7 Global interrupt
              clear</description>
              <bitOffset>24</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTCIF1</name>
              <description>Channel 1 Transfer Complete
              clear</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTCIF2</name>
              <description>Channel 2 Transfer Complete
              clear</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTCIF3</name>
              <description>Channel 3 Transfer Complete
              clear</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTCIF4</name>
              <description>Channel 4 Transfer Complete
              clear</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTCIF5</name>
              <description>Channel 5 Transfer Complete
              clear</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTCIF6</name>
              <description>Channel 6 Transfer Complete
              clear</description>
              <bitOffset>21</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTCIF7</name>
              <description>Channel 7 Transfer Complete
              clear</description>
              <bitOffset>25</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CHTIF1</name>
              <description>Channel 1 Half Transfer
              clear</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CHTIF2</name>
              <description>Channel 2 Half Transfer
              clear</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CHTIF3</name>
              <description>Channel 3 Half Transfer
              clear</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CHTIF4</name>
              <description>Channel 4 Half Transfer
              clear</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CHTIF5</name>
              <description>Channel 5 Half Transfer
              clear</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CHTIF6</name>
              <description>Channel 6 Half Transfer
              clear</description>
              <bitOffset>22</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CHTIF7</name>
              <description>Channel 7 Half Transfer
              clear</description>
              <bitOffset>26</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTEIF1</name>
              <description>Channel 1 Transfer Error
              clear</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTEIF2</name>
              <description>Channel 2 Transfer Error
              clear</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTEIF3</name>
              <description>Channel 3 Transfer Error
              clear</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTEIF4</name>
              <description>Channel 4 Transfer Error
              clear</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTEIF5</name>
              <description>Channel 5 Transfer Error
              clear</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTEIF6</name>
              <description>Channel 6 Transfer Error
              clear</description>
              <bitOffset>23</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTEIF7</name>
              <description>Channel 7 Transfer Error
              clear</description>
              <bitOffset>27</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR1</name>
          <displayName>CCR1</displayName>
          <description>DMA channel configuration register
          (DMA_CCR)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EN</name>
              <description>Channel enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transfer complete interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIE</name>
              <description>Half Transfer interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIE</name>
              <description>Transfer error interrupt
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DIR</name>
              <description>Data transfer direction</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CIRC</name>
              <description>Circular mode</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PINC</name>
              <description>Peripheral increment mode</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MINC</name>
              <description>Memory increment mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PSIZE</name>
              <description>Peripheral size</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MSIZE</name>
              <description>Memory size</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PL</name>
              <description>Channel Priority level</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MEM2MEM</name>
              <description>Memory to memory mode</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNDTR1</name>
          <displayName>CNDTR1</displayName>
          <description>DMA channel 1 number of data
          register</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>NDT</name>
              <description>Number of data to transfer</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CPAR1</name>
          <displayName>CPAR1</displayName>
          <description>DMA channel 1 peripheral address
          register</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PA</name>
              <description>Peripheral address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CMAR1</name>
          <displayName>CMAR1</displayName>
          <description>DMA channel 1 memory address
          register</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MA</name>
              <description>Memory address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR2</name>
          <displayName>CCR2</displayName>
          <description>DMA channel configuration register
          (DMA_CCR)</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EN</name>
              <description>Channel enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transfer complete interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIE</name>
              <description>Half Transfer interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIE</name>
              <description>Transfer error interrupt
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DIR</name>
              <description>Data transfer direction</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CIRC</name>
              <description>Circular mode</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PINC</name>
              <description>Peripheral increment mode</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MINC</name>
              <description>Memory increment mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PSIZE</name>
              <description>Peripheral size</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MSIZE</name>
              <description>Memory size</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PL</name>
              <description>Channel Priority level</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MEM2MEM</name>
              <description>Memory to memory mode</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNDTR2</name>
          <displayName>CNDTR2</displayName>
          <description>DMA channel 2 number of data
          register</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>NDT</name>
              <description>Number of data to transfer</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CPAR2</name>
          <displayName>CPAR2</displayName>
          <description>DMA channel 2 peripheral address
          register</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PA</name>
              <description>Peripheral address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CMAR2</name>
          <displayName>CMAR2</displayName>
          <description>DMA channel 2 memory address
          register</description>
          <addressOffset>0x28</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MA</name>
              <description>Memory address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR3</name>
          <displayName>CCR3</displayName>
          <description>DMA channel configuration register
          (DMA_CCR)</description>
          <addressOffset>0x30</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EN</name>
              <description>Channel enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transfer complete interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIE</name>
              <description>Half Transfer interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIE</name>
              <description>Transfer error interrupt
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DIR</name>
              <description>Data transfer direction</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CIRC</name>
              <description>Circular mode</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PINC</name>
              <description>Peripheral increment mode</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MINC</name>
              <description>Memory increment mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PSIZE</name>
              <description>Peripheral size</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MSIZE</name>
              <description>Memory size</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PL</name>
              <description>Channel Priority level</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MEM2MEM</name>
              <description>Memory to memory mode</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNDTR3</name>
          <displayName>CNDTR3</displayName>
          <description>DMA channel 3 number of data
          register</description>
          <addressOffset>0x34</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>NDT</name>
              <description>Number of data to transfer</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CPAR3</name>
          <displayName>CPAR3</displayName>
          <description>DMA channel 3 peripheral address
          register</description>
          <addressOffset>0x38</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PA</name>
              <description>Peripheral address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CMAR3</name>
          <displayName>CMAR3</displayName>
          <description>DMA channel 3 memory address
          register</description>
          <addressOffset>0x3C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MA</name>
              <description>Memory address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR4</name>
          <displayName>CCR4</displayName>
          <description>DMA channel configuration register
          (DMA_CCR)</description>
          <addressOffset>0x44</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EN</name>
              <description>Channel enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transfer complete interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIE</name>
              <description>Half Transfer interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIE</name>
              <description>Transfer error interrupt
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DIR</name>
              <description>Data transfer direction</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CIRC</name>
              <description>Circular mode</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PINC</name>
              <description>Peripheral increment mode</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MINC</name>
              <description>Memory increment mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PSIZE</name>
              <description>Peripheral size</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MSIZE</name>
              <description>Memory size</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PL</name>
              <description>Channel Priority level</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MEM2MEM</name>
              <description>Memory to memory mode</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNDTR4</name>
          <displayName>CNDTR4</displayName>
          <description>DMA channel 4 number of data
          register</description>
          <addressOffset>0x48</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>NDT</name>
              <description>Number of data to transfer</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CPAR4</name>
          <displayName>CPAR4</displayName>
          <description>DMA channel 4 peripheral address
          register</description>
          <addressOffset>0x4C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PA</name>
              <description>Peripheral address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CMAR4</name>
          <displayName>CMAR4</displayName>
          <description>DMA channel 4 memory address
          register</description>
          <addressOffset>0x50</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MA</name>
              <description>Memory address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR5</name>
          <displayName>CCR5</displayName>
          <description>DMA channel configuration register
          (DMA_CCR)</description>
          <addressOffset>0x58</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EN</name>
              <description>Channel enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transfer complete interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIE</name>
              <description>Half Transfer interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIE</name>
              <description>Transfer error interrupt
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DIR</name>
              <description>Data transfer direction</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CIRC</name>
              <description>Circular mode</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PINC</name>
              <description>Peripheral increment mode</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MINC</name>
              <description>Memory increment mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PSIZE</name>
              <description>Peripheral size</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MSIZE</name>
              <description>Memory size</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PL</name>
              <description>Channel Priority level</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MEM2MEM</name>
              <description>Memory to memory mode</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNDTR5</name>
          <displayName>CNDTR5</displayName>
          <description>DMA channel 5 number of data
          register</description>
          <addressOffset>0x5C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>NDT</name>
              <description>Number of data to transfer</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CPAR5</name>
          <displayName>CPAR5</displayName>
          <description>DMA channel 5 peripheral address
          register</description>
          <addressOffset>0x60</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PA</name>
              <description>Peripheral address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CMAR5</name>
          <displayName>CMAR5</displayName>
          <description>DMA channel 5 memory address
          register</description>
          <addressOffset>0x64</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MA</name>
              <description>Memory address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR6</name>
          <displayName>CCR6</displayName>
          <description>DMA channel configuration register
          (DMA_CCR)</description>
          <addressOffset>0x6C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EN</name>
              <description>Channel enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transfer complete interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIE</name>
              <description>Half Transfer interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIE</name>
              <description>Transfer error interrupt
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DIR</name>
              <description>Data transfer direction</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CIRC</name>
              <description>Circular mode</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PINC</name>
              <description>Peripheral increment mode</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MINC</name>
              <description>Memory increment mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PSIZE</name>
              <description>Peripheral size</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MSIZE</name>
              <description>Memory size</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PL</name>
              <description>Channel Priority level</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MEM2MEM</name>
              <description>Memory to memory mode</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNDTR6</name>
          <displayName>CNDTR6</displayName>
          <description>DMA channel 6 number of data
          register</description>
          <addressOffset>0x70</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>NDT</name>
              <description>Number of data to transfer</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CPAR6</name>
          <displayName>CPAR6</displayName>
          <description>DMA channel 6 peripheral address
          register</description>
          <addressOffset>0x74</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PA</name>
              <description>Peripheral address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CMAR6</name>
          <displayName>CMAR6</displayName>
          <description>DMA channel 6 memory address
          register</description>
          <addressOffset>0x78</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MA</name>
              <description>Memory address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR7</name>
          <displayName>CCR7</displayName>
          <description>DMA channel configuration register
          (DMA_CCR)</description>
          <addressOffset>0x80</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EN</name>
              <description>Channel enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transfer complete interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HTIE</name>
              <description>Half Transfer interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEIE</name>
              <description>Transfer error interrupt
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DIR</name>
              <description>Data transfer direction</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CIRC</name>
              <description>Circular mode</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PINC</name>
              <description>Peripheral increment mode</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MINC</name>
              <description>Memory increment mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PSIZE</name>
              <description>Peripheral size</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MSIZE</name>
              <description>Memory size</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PL</name>
              <description>Channel Priority level</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MEM2MEM</name>
              <description>Memory to memory mode</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNDTR7</name>
          <displayName>CNDTR7</displayName>
          <description>DMA channel 7 number of data
          register</description>
          <addressOffset>0x84</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>NDT</name>
              <description>Number of data to transfer</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CPAR7</name>
          <displayName>CPAR7</displayName>
          <description>DMA channel 7 peripheral address
          register</description>
          <addressOffset>0x88</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PA</name>
              <description>Peripheral address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CMAR7</name>
          <displayName>CMAR7</displayName>
          <description>DMA channel 7 memory address
          register</description>
          <addressOffset>0x8C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>MA</name>
              <description>Memory address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="DMA1">
      <name>DMA2</name>
      <baseAddress>0x40020400</baseAddress>
      <interrupt>
        <name>DMA2_CH1</name>
        <description>DMA2 Channel 1 interrupt</description>
        <value>50</value>
      </interrupt>
      <interrupt>
        <name>DMA2_CH2</name>
        <description>DMA2 Channel 2 interrupt</description>
        <value>51</value>
      </interrupt>
      <interrupt>
        <name>DMA2_CH3</name>
        <description>DMA2 Channel 3 interrupt</description>
        <value>52</value>
      </interrupt>
      <interrupt>
        <name>DMA2_CH4</name>
        <description>DMA2 Channel 4 interrupt</description>
        <value>53</value>
      </interrupt>
      <interrupt>
        <name>DMA2_CH5</name>
        <description>DMA2 Channel 5 interrupt</description>
        <value>54</value>
      </interrupt>
    </peripheral>
    <peripheral>
      <name>RTC</name>
      <description>Real time clock</description>
      <groupName>RTC</groupName>
      <baseAddress>0x40002800</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>RTC_WKUP</name>
        <description>RTC Wakeup through EXTI line
        interrupt</description>
        <value>3</value>
      </interrupt>
      <interrupt>
        <name>RTC_Alarm</name>
        <description>RTC Alarms (A and B) through EXTI line
        interrupt</description>
        <value>41</value>
      </interrupt>
      <registers>
        <register>
          <name>CRH</name>
          <displayName>CRH</displayName>
          <description>RTC Control Register High</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SECIE</name>
              <description>Second interrupt Enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ALRIE</name>
              <description>Alarm interrupt Enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OWIE</name>
              <description>Overflow interrupt Enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CRL</name>
          <displayName>CRL</displayName>
          <description>RTC Control Register Low</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000020</resetValue>
          <fields>
            <field>
              <name>SECF</name>
              <description>Second Flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ALRF</name>
              <description>Alarm Flag</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>OWF</name>
              <description>Overflow Flag</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>RSF</name>
              <description>Registers Synchronized
              Flag</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>CNF</name>
              <description>Configuration Flag</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>RTOFF</name>
              <description>RTC operation OFF</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>PRLH</name>
          <displayName>PRLH</displayName>
          <description>RTC Prescaler Load Register
          High</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PRLH</name>
              <description>RTC Prescaler Load Register
              High</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PRLL</name>
          <displayName>PRLL</displayName>
          <description>RTC Prescaler Load Register
          Low</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x8000</resetValue>
          <fields>
            <field>
              <name>PRLL</name>
              <description>RTC Prescaler Divider Register
              Low</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DIVH</name>
          <displayName>DIVH</displayName>
          <description>RTC Prescaler Divider Register
          High</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DIVH</name>
              <description>RTC prescaler divider register
              high</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DIVL</name>
          <displayName>DIVL</displayName>
          <description>RTC Prescaler Divider Register
          Low</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x8000</resetValue>
          <fields>
            <field>
              <name>DIVL</name>
              <description>RTC prescaler divider register
              Low</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNTH</name>
          <displayName>CNTH</displayName>
          <description>RTC Counter Register High</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CNTH</name>
              <description>RTC counter register high</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNTL</name>
          <displayName>CNTL</displayName>
          <description>RTC Counter Register Low</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CNTL</name>
              <description>RTC counter register Low</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ALRH</name>
          <displayName>ALRH</displayName>
          <description>RTC Alarm Register High</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0xFFFF</resetValue>
          <fields>
            <field>
              <name>ALRH</name>
              <description>RTC alarm register high</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ALRL</name>
          <displayName>ALRL</displayName>
          <description>RTC Alarm Register Low</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0xFFFF</resetValue>
          <fields>
            <field>
              <name>ALRL</name>
              <description>RTC alarm register low</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>IWDG</name>
      <description>Independent watchdog</description>
      <groupName>IWDG</groupName>
      <baseAddress>0x40003000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>KR</name>
          <displayName>KR</displayName>
          <description>Key register (IWDG_KR)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>KEY</name>
              <description>Key value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PR</name>
          <displayName>PR</displayName>
          <description>Prescaler register (IWDG_PR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PR</name>
              <description>Prescaler divider</description>
              <bitOffset>0</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>RLR</name>
          <displayName>RLR</displayName>
          <description>Reload register (IWDG_RLR)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000FFF</resetValue>
          <fields>
            <field>
              <name>RL</name>
              <description>Watchdog counter reload
              value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>Status register (IWDG_SR)</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>PVU</name>
              <description>Watchdog prescaler value
              update</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RVU</name>
              <description>Watchdog counter reload value
              update</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>WWDG</name>
      <description>Window watchdog</description>
      <groupName>WWDG</groupName>
      <baseAddress>0x40002C00</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>CR</name>
          <displayName>CR</displayName>
          <description>Control register (WWDG_CR)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000007F</resetValue>
          <fields>
            <field>
              <name>T</name>
              <description>7-bit counter (MSB to LSB)</description>
              <bitOffset>0</bitOffset>
              <bitWidth>7</bitWidth>
            </field>
            <field>
              <name>WDGA</name>
              <description>Activation bit</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CFR</name>
          <displayName>CFR</displayName>
          <description>Configuration register
          (WWDG_CFR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000007F</resetValue>
          <fields>
            <field>
              <name>W</name>
              <description>7-bit window value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>7</bitWidth>
            </field>
            <field>
              <name>WDGTB</name>
              <description>Timer Base</description>
              <bitOffset>7</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>EWI</name>
              <description>Early Wakeup Interrupt</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>Status register (WWDG_SR)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EWI</name>
              <description>Early Wakeup Interrupt</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>TIM2</name>
      <description>General purpose timer</description>
      <groupName>TIM</groupName>
      <baseAddress>0x40000000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>WWDG</name>
        <description>Window Watchdog interrupt</description>
        <value>0</value>
      </interrupt>
      <registers>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>control register 1</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CKD</name>
              <description>Clock division</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>ARPE</name>
              <description>Auto-reload preload enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CMS</name>
              <description>Center-aligned mode
              selection</description>
              <bitOffset>5</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DIR</name>
              <description>Direction</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OPM</name>
              <description>One-pulse mode</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>URS</name>
              <description>Update request source</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UDIS</name>
              <description>Update disable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CEN</name>
              <description>Counter enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>control register 2</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>TI1S</name>
              <description>TI1 selection</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MMS</name>
              <description>Master mode selection</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>CCDS</name>
              <description>Capture/compare DMA
              selection</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SMCR</name>
          <displayName>SMCR</displayName>
          <description>slave mode control register</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>ETP</name>
              <description>External trigger polarity</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ECE</name>
              <description>External clock enable</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ETPS</name>
              <description>External trigger prescaler</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>ETF</name>
              <description>External trigger filter</description>
              <bitOffset>8</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>MSM</name>
              <description>Master/Slave mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TS</name>
              <description>Trigger selection</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMS</name>
              <description>Slave mode selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DIER</name>
          <displayName>DIER</displayName>
          <description>DMA/Interrupt enable register</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>TDE</name>
              <description>Trigger DMA request enable</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC4DE</name>
              <description>Capture/Compare 4 DMA request
              enable</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC3DE</name>
              <description>Capture/Compare 3 DMA request
              enable</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2DE</name>
              <description>Capture/Compare 2 DMA request
              enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1DE</name>
              <description>Capture/Compare 1 DMA request
              enable</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UDE</name>
              <description>Update DMA request enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIE</name>
              <description>Trigger interrupt enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC4IE</name>
              <description>Capture/Compare 4 interrupt
              enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC3IE</name>
              <description>Capture/Compare 3 interrupt
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2IE</name>
              <description>Capture/Compare 2 interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1IE</name>
              <description>Capture/Compare 1 interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UIE</name>
              <description>Update interrupt enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>status register</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CC4OF</name>
              <description>Capture/Compare 4 overcapture
              flag</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC3OF</name>
              <description>Capture/Compare 3 overcapture
              flag</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2OF</name>
              <description>Capture/compare 2 overcapture
              flag</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1OF</name>
              <description>Capture/Compare 1 overcapture
              flag</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIF</name>
              <description>Trigger interrupt flag</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC4IF</name>
              <description>Capture/Compare 4 interrupt
              flag</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC3IF</name>
              <description>Capture/Compare 3 interrupt
              flag</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2IF</name>
              <description>Capture/Compare 2 interrupt
              flag</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1IF</name>
              <description>Capture/compare 1 interrupt
              flag</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UIF</name>
              <description>Update interrupt flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>EGR</name>
          <displayName>EGR</displayName>
          <description>event generation register</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>TG</name>
              <description>Trigger generation</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC4G</name>
              <description>Capture/compare 4
              generation</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC3G</name>
              <description>Capture/compare 3
              generation</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2G</name>
              <description>Capture/compare 2
              generation</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1G</name>
              <description>Capture/compare 1
              generation</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UG</name>
              <description>Update generation</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCMR1_Output</name>
          <displayName>CCMR1_Output</displayName>
          <description>capture/compare mode register 1 (output
          mode)</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>OC2CE</name>
              <description>Output compare 2 clear
              enable</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC2M</name>
              <description>Output compare 2 mode</description>
              <bitOffset>12</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>OC2PE</name>
              <description>Output compare 2 preload
              enable</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC2FE</name>
              <description>Output compare 2 fast
              enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2S</name>
              <description>Capture/Compare 2
              selection</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>OC1CE</name>
              <description>Output compare 1 clear
              enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC1M</name>
              <description>Output compare 1 mode</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>OC1PE</name>
              <description>Output compare 1 preload
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC1FE</name>
              <description>Output compare 1 fast
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1S</name>
              <description>Capture/Compare 1
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCMR1_Input</name>
          <displayName>CCMR1_Input</displayName>
          <description>capture/compare mode register 1 (input
          mode)</description>
          <alternateRegister>CCMR1_Output</alternateRegister>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IC2F</name>
              <description>Input capture 2 filter</description>
              <bitOffset>12</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>IC2PSC</name>
              <description>Input capture 2 prescaler</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CC2S</name>
              <description>Capture/compare 2
              selection</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>IC1F</name>
              <description>Input capture 1 filter</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>IC1PSC</name>
              <description>Input capture 1 prescaler</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CC1S</name>
              <description>Capture/Compare 1
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCMR2_Output</name>
          <displayName>CCMR2_Output</displayName>
          <description>capture/compare mode register 2 (output
          mode)</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>O24CE</name>
              <description>Output compare 4 clear
              enable</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC4M</name>
              <description>Output compare 4 mode</description>
              <bitOffset>12</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>OC4PE</name>
              <description>Output compare 4 preload
              enable</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC4FE</name>
              <description>Output compare 4 fast
              enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC4S</name>
              <description>Capture/Compare 4
              selection</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>OC3CE</name>
              <description>Output compare 3 clear
              enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC3M</name>
              <description>Output compare 3 mode</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>OC3PE</name>
              <description>Output compare 3 preload
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC3FE</name>
              <description>Output compare 3 fast
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC3S</name>
              <description>Capture/Compare 3
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCMR2_Input</name>
          <displayName>CCMR2_Input</displayName>
          <description>capture/compare mode register 2 (input
          mode)</description>
          <alternateRegister>CCMR2_Output</alternateRegister>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IC4F</name>
              <description>Input capture 4 filter</description>
              <bitOffset>12</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>IC4PSC</name>
              <description>Input capture 4 prescaler</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CC4S</name>
              <description>Capture/Compare 4
              selection</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>IC3F</name>
              <description>Input capture 3 filter</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>IC3PSC</name>
              <description>Input capture 3 prescaler</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CC3S</name>
              <description>Capture/Compare 3
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCER</name>
          <displayName>CCER</displayName>
          <description>capture/compare enable
          register</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CC4P</name>
              <description>Capture/Compare 3 output
              Polarity</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC4E</name>
              <description>Capture/Compare 4 output
              enable</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC3P</name>
              <description>Capture/Compare 3 output
              Polarity</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC3E</name>
              <description>Capture/Compare 3 output
              enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2P</name>
              <description>Capture/Compare 2 output
              Polarity</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2E</name>
              <description>Capture/Compare 2 output
              enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1P</name>
              <description>Capture/Compare 1 output
              Polarity</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1E</name>
              <description>Capture/Compare 1 output
              enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNT</name>
          <displayName>CNT</displayName>
          <description>counter</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CNT</name>
              <description>counter value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PSC</name>
          <displayName>PSC</displayName>
          <description>prescaler</description>
          <addressOffset>0x28</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>PSC</name>
              <description>Prescaler value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ARR</name>
          <displayName>ARR</displayName>
          <description>auto-reload register</description>
          <addressOffset>0x2C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ARR</name>
              <description>Auto-reload value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR1</name>
          <displayName>CCR1</displayName>
          <description>capture/compare register 1</description>
          <addressOffset>0x34</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CCR1</name>
              <description>Capture/Compare 1 value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR2</name>
          <displayName>CCR2</displayName>
          <description>capture/compare register 2</description>
          <addressOffset>0x38</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CCR2</name>
              <description>Capture/Compare 2 value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR3</name>
          <displayName>CCR3</displayName>
          <description>capture/compare register 3</description>
          <addressOffset>0x3C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CCR3</name>
              <description>Capture/Compare value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR4</name>
          <displayName>CCR4</displayName>
          <description>capture/compare register 4</description>
          <addressOffset>0x40</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CCR4</name>
              <description>Capture/Compare value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DCR</name>
          <displayName>DCR</displayName>
          <description>DMA control register</description>
          <addressOffset>0x48</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>DBL</name>
              <description>DMA burst length</description>
              <bitOffset>8</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>DBA</name>
              <description>DMA base address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DMAR</name>
          <displayName>DMAR</displayName>
          <description>DMA address for full transfer</description>
          <addressOffset>0x4C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>DMAB</name>
              <description>DMA register for burst
              accesses</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="TIM2">
      <name>TIM3</name>
      <baseAddress>0x40000400</baseAddress>
      <interrupt>
        <name>TIM2</name>
        <description>TIM2 global interrupt</description>
        <value>28</value>
      </interrupt>
    </peripheral>
    <peripheral derivedFrom="TIM2">
      <name>TIM4</name>
      <baseAddress>0x40000800</baseAddress>
      <interrupt>
        <name>TIM3</name>
        <description>TIM3 global interrupt</description>
        <value>29</value>
      </interrupt>
    </peripheral>
    <peripheral derivedFrom="TIM2">
      <name>TIM5</name>
      <baseAddress>0x40000C00</baseAddress>
      <interrupt>
        <name>TIM4</name>
        <description>TIM4 global interrupt</description>
        <value>30</value>
      </interrupt>
    </peripheral>
    <peripheral>
      <name>TIM9</name>
      <description>General purpose timer</description>
      <groupName>TIM</groupName>
      <baseAddress>0x40014C00</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>TIM5</name>
        <description>TIM5 global interrupt</description>
        <value>46</value>
      </interrupt>
      <registers>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>control register 1</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CKD</name>
              <description>Clock division</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>ARPE</name>
              <description>Auto-reload preload enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OPM</name>
              <description>One-pulse mode</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>URS</name>
              <description>Update request source</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UDIS</name>
              <description>Update disable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CEN</name>
              <description>Counter enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>control register 2</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>MMS</name>
              <description>Master mode selection</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SMCR</name>
          <displayName>SMCR</displayName>
          <description>slave mode control register</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>MSM</name>
              <description>Master/Slave mode</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TS</name>
              <description>Trigger selection</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMS</name>
              <description>Slave mode selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DIER</name>
          <displayName>DIER</displayName>
          <description>DMA/Interrupt enable register</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>TIE</name>
              <description>Trigger interrupt enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2IE</name>
              <description>Capture/Compare 2 interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1IE</name>
              <description>Capture/Compare 1 interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UIE</name>
              <description>Update interrupt enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>status register</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CC2OF</name>
              <description>Capture/compare 2 overcapture
              flag</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1OF</name>
              <description>Capture/Compare 1 overcapture
              flag</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TIF</name>
              <description>Trigger interrupt flag</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2IF</name>
              <description>Capture/Compare 2 interrupt
              flag</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1IF</name>
              <description>Capture/compare 1 interrupt
              flag</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UIF</name>
              <description>Update interrupt flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>EGR</name>
          <displayName>EGR</displayName>
          <description>event generation register</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>TG</name>
              <description>Trigger generation</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2G</name>
              <description>Capture/compare 2
              generation</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1G</name>
              <description>Capture/compare 1
              generation</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UG</name>
              <description>Update generation</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCMR1_Output</name>
          <displayName>CCMR1_Output</displayName>
          <description>capture/compare mode register 1 (output
          mode)</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>OC2M</name>
              <description>Output Compare 2 mode</description>
              <bitOffset>12</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>OC2PE</name>
              <description>Output Compare 2 preload
              enable</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC2FE</name>
              <description>Output Compare 2 fast
              enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2S</name>
              <description>Capture/Compare 2
              selection</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>OC1M</name>
              <description>Output Compare 1 mode</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>OC1PE</name>
              <description>Output Compare 1 preload
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OC1FE</name>
              <description>Output Compare 1 fast
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1S</name>
              <description>Capture/Compare 1
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCMR1_Input</name>
          <displayName>CCMR1_Input</displayName>
          <description>capture/compare mode register 1 (input
          mode)</description>
          <alternateRegister>CCMR1_Output</alternateRegister>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IC2F</name>
              <description>Input capture 2 filter</description>
              <bitOffset>12</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>IC2PSC</name>
              <description>Input capture 2 prescaler</description>
              <bitOffset>10</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CC2S</name>
              <description>Capture/Compare 2
              selection</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>IC1F</name>
              <description>Input capture 1 filter</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>IC1PSC</name>
              <description>Input capture 1 prescaler</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CC1S</name>
              <description>Capture/Compare 1
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCER</name>
          <displayName>CCER</displayName>
          <description>capture/compare enable
          register</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CC2NP</name>
              <description>Capture/Compare 2 output
              Polarity</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2P</name>
              <description>Capture/Compare 2 output
              Polarity</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC2E</name>
              <description>Capture/Compare 2 output
              enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1NP</name>
              <description>Capture/Compare 1 output
              Polarity</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1P</name>
              <description>Capture/Compare 1 output
              Polarity</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1E</name>
              <description>Capture/Compare 1 output
              enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNT</name>
          <displayName>CNT</displayName>
          <description>counter</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CNT</name>
              <description>counter value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PSC</name>
          <displayName>PSC</displayName>
          <description>prescaler</description>
          <addressOffset>0x28</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>PSC</name>
              <description>Prescaler value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ARR</name>
          <displayName>ARR</displayName>
          <description>auto-reload register</description>
          <addressOffset>0x2C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ARR</name>
              <description>Auto-reload value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR1</name>
          <displayName>CCR1</displayName>
          <description>capture/compare register 1</description>
          <addressOffset>0x34</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CCR1</name>
              <description>Capture/Compare 1 value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR2</name>
          <displayName>CCR2</displayName>
          <description>capture/compare register 2</description>
          <addressOffset>0x38</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CCR2</name>
              <description>Capture/Compare 2 value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="TIM9">
      <name>TIM12</name>
      <baseAddress>0x40001800</baseAddress>
      <interrupt>
        <name>TIM9</name>
        <description>TIM9 global interrupt</description>
        <value>25</value>
      </interrupt>
    </peripheral>
    <peripheral>
      <name>TIM10</name>
      <description>General purpose timer</description>
      <groupName>TIM</groupName>
      <baseAddress>0x40015000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>control register 1</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CKD</name>
              <description>Clock division</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>ARPE</name>
              <description>Auto-reload preload enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>URS</name>
              <description>Update request source</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UDIS</name>
              <description>Update disable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CEN</name>
              <description>Counter enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>control register 2</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>MMS</name>
              <description>Master mode selection</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DIER</name>
          <displayName>DIER</displayName>
          <description>DMA/Interrupt enable register</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CC1IE</name>
              <description>Capture/Compare 1 interrupt
              enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UIE</name>
              <description>Update interrupt enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>status register</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CC1OF</name>
              <description>Capture/Compare 1 overcapture
              flag</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1IF</name>
              <description>Capture/compare 1 interrupt
              flag</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UIF</name>
              <description>Update interrupt flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>EGR</name>
          <displayName>EGR</displayName>
          <description>event generation register</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CC1G</name>
              <description>Capture/compare 1
              generation</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UG</name>
              <description>Update generation</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCMR1_Output</name>
          <displayName>CCMR1_Output</displayName>
          <description>capture/compare mode register (output
          mode)</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>OC1M</name>
              <description>Output Compare 1 mode</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>OC1PE</name>
              <description>Output Compare 1 preload
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1S</name>
              <description>Capture/Compare 1
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCMR1_Input</name>
          <displayName>CCMR1_Input</displayName>
          <description>capture/compare mode register (input
          mode)</description>
          <alternateRegister>CCMR1_Output</alternateRegister>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IC1F</name>
              <description>Input capture 1 filter</description>
              <bitOffset>4</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>IC1PSC</name>
              <description>Input capture 1 prescaler</description>
              <bitOffset>2</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CC1S</name>
              <description>Capture/Compare 1
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCER</name>
          <displayName>CCER</displayName>
          <description>capture/compare enable
          register</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CC1NP</name>
              <description>Capture/Compare 1 output
              Polarity</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1P</name>
              <description>Capture/Compare 1 output
              Polarity</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CC1E</name>
              <description>Capture/Compare 1 output
              enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNT</name>
          <displayName>CNT</displayName>
          <description>counter</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CNT</name>
              <description>counter value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PSC</name>
          <displayName>PSC</displayName>
          <description>prescaler</description>
          <addressOffset>0x28</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>PSC</name>
              <description>Prescaler value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ARR</name>
          <displayName>ARR</displayName>
          <description>auto-reload register</description>
          <addressOffset>0x2C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ARR</name>
              <description>Auto-reload value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR1</name>
          <displayName>CCR1</displayName>
          <description>capture/compare register 1</description>
          <addressOffset>0x34</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CCR1</name>
              <description>Capture/Compare 1 value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="TIM10">
      <name>TIM11</name>
      <baseAddress>0x40015400</baseAddress>
      <interrupt>
        <name>TIM10</name>
        <description>TIM10 global interrupt</description>
        <value>26</value>
      </interrupt>
    </peripheral>
    <peripheral derivedFrom="TIM10">
      <name>TIM13</name>
      <baseAddress>0x40001C00</baseAddress>
      <interrupt>
        <name>TIM11</name>
        <description>TIM11 global interrupt</description>
        <value>27</value>
      </interrupt>
    </peripheral>
    <peripheral derivedFrom="TIM10">
      <name>TIM14</name>
      <baseAddress>0x40002000</baseAddress>
    </peripheral>
    <peripheral>
      <name>TIM6</name>
      <description>Basic timer</description>
      <groupName>TIM</groupName>
      <baseAddress>0x40001000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>control register 1</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>ARPE</name>
              <description>Auto-reload preload enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OPM</name>
              <description>One-pulse mode</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>URS</name>
              <description>Update request source</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UDIS</name>
              <description>Update disable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CEN</name>
              <description>Counter enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>control register 2</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>MMS</name>
              <description>Master mode selection</description>
              <bitOffset>4</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DIER</name>
          <displayName>DIER</displayName>
          <description>DMA/Interrupt enable register</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>UDE</name>
              <description>Update DMA request enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UIE</name>
              <description>Update interrupt enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>status register</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>UIF</name>
              <description>Update interrupt flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>EGR</name>
          <displayName>EGR</displayName>
          <description>event generation register</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>UG</name>
              <description>Update generation</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CNT</name>
          <displayName>CNT</displayName>
          <description>counter</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CNT</name>
              <description>Low counter value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>PSC</name>
          <displayName>PSC</displayName>
          <description>prescaler</description>
          <addressOffset>0x28</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>PSC</name>
              <description>Prescaler value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ARR</name>
          <displayName>ARR</displayName>
          <description>auto-reload register</description>
          <addressOffset>0x2C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ARR</name>
              <description>Low Auto-reload value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="TIM6">
      <name>TIM7</name>
      <baseAddress>0x40001400</baseAddress>
      <interrupt>
        <name>TIM6</name>
        <description>TIM6 global interrupt</description>
        <value>43</value>
      </interrupt>
    </peripheral>
    <peripheral>
      <name>I2C1</name>
      <description>Inter integrated circuit</description>
      <groupName>I2C</groupName>
      <baseAddress>0x40005400</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>TIM7</name>
        <description>TIM7 global interrupt</description>
        <value>44</value>
      </interrupt>
      <registers>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>Control register 1</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>SWRST</name>
              <description>Software reset</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ALERT</name>
              <description>SMBus alert</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PEC</name>
              <description>Packet error checking</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>POS</name>
              <description>Acknowledge/PEC Position (for data
              reception)</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ACK</name>
              <description>Acknowledge enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>STOP</name>
              <description>Stop generation</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>START</name>
              <description>Start generation</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>NOSTRETCH</name>
              <description>Clock stretching disable (Slave
              mode)</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ENGC</name>
              <description>General call enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ENPEC</name>
              <description>PEC enable</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ENARP</name>
              <description>ARP enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SMBTYPE</name>
              <description>SMBus type</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SMBUS</name>
              <description>SMBus mode</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PE</name>
              <description>Peripheral enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>Control register 2</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>LAST</name>
              <description>DMA last transfer</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DMAEN</name>
              <description>DMA requests enable</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ITBUFEN</name>
              <description>Buffer interrupt enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ITEVTEN</name>
              <description>Event interrupt enable</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ITERREN</name>
              <description>Error interrupt enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>FREQ</name>
              <description>Peripheral clock frequency</description>
              <bitOffset>0</bitOffset>
              <bitWidth>6</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>OAR1</name>
          <displayName>OAR1</displayName>
          <description>Own address register 1</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>ADDMODE</name>
              <description>Addressing mode (slave
              mode)</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ADD10</name>
              <description>Interface address</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>ADD7</name>
              <description>Interface address</description>
              <bitOffset>1</bitOffset>
              <bitWidth>7</bitWidth>
            </field>
            <field>
              <name>ADD0</name>
              <description>Interface address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>OAR2</name>
          <displayName>OAR2</displayName>
          <description>Own address register 2</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>ADD2</name>
              <description>Interface address</description>
              <bitOffset>1</bitOffset>
              <bitWidth>7</bitWidth>
            </field>
            <field>
              <name>ENDUAL</name>
              <description>Dual addressing mode
              enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR</name>
          <displayName>DR</displayName>
          <description>Data register</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>DR</name>
              <description>8-bit data register</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR1</name>
          <displayName>SR1</displayName>
          <description>Status register 1</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>SMBALERT</name>
              <description>SMBus alert</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TIMEOUT</name>
              <description>Timeout or Tlow error</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PECERR</name>
              <description>PEC Error in reception</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>OVR</name>
              <description>Overrun/Underrun</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>AF</name>
              <description>Acknowledge failure</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>ARLO</name>
              <description>Arbitration lost (master
              mode)</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>BERR</name>
              <description>Bus error</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TxE</name>
              <description>Data register empty
              (transmitters)</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>RxNE</name>
              <description>Data register not empty
              (receivers)</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>STOPF</name>
              <description>Stop detection (slave
              mode)</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>ADD10</name>
              <description>10-bit header sent (Master
              mode)</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>BTF</name>
              <description>Byte transfer finished</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>ADDR</name>
              <description>Address sent (master mode)/matched
              (slave mode)</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>SB</name>
              <description>Start bit (Master mode)</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>SR2</name>
          <displayName>SR2</displayName>
          <description>Status register 2</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>PEC</name>
              <description>acket error checking
              register</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>DUALF</name>
              <description>Dual flag (Slave mode)</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SMBHOST</name>
              <description>SMBus host header (Slave
              mode)</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SMBDEFAULT</name>
              <description>SMBus device default address (Slave
              mode)</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>GENCALL</name>
              <description>General call address (Slave
              mode)</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TRA</name>
              <description>Transmitter/receiver</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BUSY</name>
              <description>Bus busy</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MSL</name>
              <description>Master/slave</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CCR</name>
          <displayName>CCR</displayName>
          <description>Clock control register</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>F_S</name>
              <description>I2C master mode selection</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DUTY</name>
              <description>Fast mode duty cycle</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CCR</name>
              <description>Clock control register in Fast/Standard
              mode (Master mode)</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>TRISE</name>
          <displayName>TRISE</displayName>
          <description>TRISE register</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0002</resetValue>
          <fields>
            <field>
              <name>TRISE</name>
              <description>Maximum rise time in Fast/Standard mode
              (Master mode)</description>
              <bitOffset>0</bitOffset>
              <bitWidth>6</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="I2C1">
      <name>I2C2</name>
      <baseAddress>0x40005800</baseAddress>
      <interrupt>
        <name>I2C1_EV</name>
        <description>I2C1 event interrupt</description>
        <value>31</value>
      </interrupt>
      <interrupt>
        <name>I2C1_ER</name>
        <description>I2C1 error interrupt</description>
        <value>32</value>
      </interrupt>
    </peripheral>
    <peripheral>
      <name>SPI1</name>
      <description>Serial peripheral interface</description>
      <groupName>SPI</groupName>
      <baseAddress>0x40013000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>I2C2_EV</name>
        <description>I2C2 event interrupt</description>
        <value>33</value>
      </interrupt>
      <interrupt>
        <name>I2C2_ER</name>
        <description>I2C2 error interrupt</description>
        <value>34</value>
      </interrupt>
      <registers>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>control register 1</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>BIDIMODE</name>
              <description>Bidirectional data mode
              enable</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BIDIOE</name>
              <description>Output enable in bidirectional
              mode</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CRCEN</name>
              <description>Hardware CRC calculation
              enable</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CRCNEXT</name>
              <description>CRC transfer next</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DFF</name>
              <description>Data frame format</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RXONLY</name>
              <description>Receive only</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SSM</name>
              <description>Software slave management</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SSI</name>
              <description>Internal slave select</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LSBFIRST</name>
              <description>Frame format</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SPE</name>
              <description>SPI enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BR</name>
              <description>Baud rate control</description>
              <bitOffset>3</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>MSTR</name>
              <description>Master selection</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CPOL</name>
              <description>Clock polarity</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CPHA</name>
              <description>Clock phase</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>control register 2</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>TXEIE</name>
              <description>Tx buffer empty interrupt
              enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RXNEIE</name>
              <description>RX buffer not empty interrupt
              enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ERRIE</name>
              <description>Error interrupt enable</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SSOE</name>
              <description>SS output enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TXDMAEN</name>
              <description>Tx buffer DMA enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RXDMAEN</name>
              <description>Rx buffer DMA enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>status register</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <resetValue>0x0002</resetValue>
          <fields>
            <field>
              <name>BSY</name>
              <description>Busy flag</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>OVR</name>
              <description>Overrun flag</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>MODF</name>
              <description>Mode fault</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>CRCERR</name>
              <description>CRC error flag</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>UDR</name>
              <description>Underrun flag</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>CHSIDE</name>
              <description>Channel side</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>TXE</name>
              <description>Transmit buffer empty</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>RXNE</name>
              <description>Receive buffer not empty</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>DR</name>
          <displayName>DR</displayName>
          <description>data register</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>DR</name>
              <description>Data register</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CRCPR</name>
          <displayName>CRCPR</displayName>
          <description>CRC polynomial register</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0007</resetValue>
          <fields>
            <field>
              <name>CRCPOLY</name>
              <description>CRC polynomial register</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>RXCRCR</name>
          <displayName>RXCRCR</displayName>
          <description>RX CRC register</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>RxCRC</name>
              <description>Rx CRC register</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>TXCRCR</name>
          <displayName>TXCRCR</displayName>
          <description>TX CRC register</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>TxCRC</name>
              <description>Tx CRC register</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>I2SCFGR</name>
          <displayName>I2SCFGR</displayName>
          <description>I2S configuration register</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>I2SMOD</name>
              <description>I2S mode selection</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>I2SE</name>
              <description>I2S Enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>I2SCFG</name>
              <description>I2S configuration mode</description>
              <bitOffset>8</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>PCMSYNC</name>
              <description>PCM frame synchronization</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>I2SSTD</name>
              <description>I2S standard selection</description>
              <bitOffset>4</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CKPOL</name>
              <description>Steady state clock
              polarity</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DATLEN</name>
              <description>Data length to be
              transferred</description>
              <bitOffset>1</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CHLEN</name>
              <description>Channel length (number of bits per audio
              channel)</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>I2SPR</name>
          <displayName>I2SPR</displayName>
          <description>I2S prescaler register</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>00000010</resetValue>
          <fields>
            <field>
              <name>MCKOE</name>
              <description>Master clock output enable</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ODD</name>
              <description>Odd factor for the
              prescaler</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>I2SDIV</name>
              <description>I2S Linear prescaler</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="SPI1">
      <name>SPI2</name>
      <baseAddress>0x40003800</baseAddress>
      <interrupt>
        <name>SPI1</name>
        <description>SPI1 global interrupt</description>
        <value>35</value>
      </interrupt>
    </peripheral>
    <peripheral derivedFrom="SPI1">
      <name>SPI3</name>
      <baseAddress>0x40003C00</baseAddress>
      <interrupt>
        <name>SPI2</name>
        <description>SPI2 global interrupt</description>
        <value>36</value>
      </interrupt>
    </peripheral>
    <peripheral>
      <name>USART1</name>
      <description>Universal synchronous asynchronous receiver
      transmitter</description>
      <groupName>USART</groupName>
      <baseAddress>0x40013800</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>SPI3</name>
        <description>SPI3 global interrupt</description>
        <value>47</value>
      </interrupt>
      <registers>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>Status register</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <resetValue>0x00C0</resetValue>
          <fields>
            <field>
              <name>CTS</name>
              <description>CTS flag</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>LBD</name>
              <description>LIN break detection flag</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TXE</name>
              <description>Transmit data register
              empty</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>TC</name>
              <description>Transmission complete</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>RXNE</name>
              <description>Read data register not
              empty</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>IDLE</name>
              <description>IDLE line detected</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>ORE</name>
              <description>Overrun error</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>NE</name>
              <description>Noise error flag</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>FE</name>
              <description>Framing error</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>PE</name>
              <description>Parity error</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>DR</name>
          <displayName>DR</displayName>
          <description>Data register</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DR</name>
              <description>Data value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>9</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BRR</name>
          <displayName>BRR</displayName>
          <description>Baud rate register</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>DIV_Mantissa</name>
              <description>mantissa of USARTDIV</description>
              <bitOffset>4</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
            <field>
              <name>DIV_Fraction</name>
              <description>fraction of USARTDIV</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>Control register 1</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>UE</name>
              <description>USART enable</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>M</name>
              <description>Word length</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAKE</name>
              <description>Wakeup method</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PCE</name>
              <description>Parity control enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PS</name>
              <description>Parity selection</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PEIE</name>
              <description>PE interrupt enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TXEIE</name>
              <description>TXE interrupt enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transmission complete interrupt
              enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RXNEIE</name>
              <description>RXNE interrupt enable</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDLEIE</name>
              <description>IDLE interrupt enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TE</name>
              <description>Transmitter enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RE</name>
              <description>Receiver enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RWU</name>
              <description>Receiver wakeup</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SBK</name>
              <description>Send break</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>Control register 2</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>LINEN</name>
              <description>LIN mode enable</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>STOP</name>
              <description>STOP bits</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>CLKEN</name>
              <description>Clock enable</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CPOL</name>
              <description>Clock polarity</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CPHA</name>
              <description>Clock phase</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LBCL</name>
              <description>Last bit clock pulse</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LBDIE</name>
              <description>LIN break detection interrupt
              enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LBDL</name>
              <description>lin break detection length</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ADD</name>
              <description>Address of the USART node</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR3</name>
          <displayName>CR3</displayName>
          <description>Control register 3</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>CTSIE</name>
              <description>CTS interrupt enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CTSE</name>
              <description>CTS enable</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RTSE</name>
              <description>RTS enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DMAT</name>
              <description>DMA enable transmitter</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DMAR</name>
              <description>DMA enable receiver</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SCEN</name>
              <description>Smartcard mode enable</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>NACK</name>
              <description>Smartcard NACK enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HDSEL</name>
              <description>Half-duplex selection</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IRLP</name>
              <description>IrDA low-power</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IREN</name>
              <description>IrDA mode enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EIE</name>
              <description>Error interrupt enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>GTPR</name>
          <displayName>GTPR</displayName>
          <description>Guard time and prescaler
          register</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0000</resetValue>
          <fields>
            <field>
              <name>GT</name>
              <description>Guard time value</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>PSC</name>
              <description>Prescaler value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral derivedFrom="USART1">
      <name>USART2</name>
      <baseAddress>0x40004400</baseAddress>
      <interrupt>
        <name>USART1</name>
        <description>USART1 global interrupt</description>
        <value>37</value>
      </interrupt>
    </peripheral>
    <peripheral derivedFrom="USART1">
      <name>USART3</name>
      <baseAddress>0x40004800</baseAddress>
      <interrupt>
        <name>USART2</name>
        <description>USART2 global interrupt</description>
        <value>38</value>
      </interrupt>
    </peripheral>
    <peripheral>
      <name>DAC</name>
      <description>Digital to analog converter</description>
      <groupName>DAC</groupName>
      <baseAddress>0x40007400</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>USART3</name>
        <description>USART3 global interrupt</description>
        <value>39</value>
      </interrupt>
      <registers>
        <register>
          <name>CR</name>
          <displayName>CR</displayName>
          <description>Control register (DAC_CR)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EN1</name>
              <description>DAC channel1 enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BOFF1</name>
              <description>DAC channel1 output buffer
              disable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEN1</name>
              <description>DAC channel1 trigger
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TSEL1</name>
              <description>DAC channel1 trigger
              selection</description>
              <bitOffset>3</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>WAVE1</name>
              <description>DAC channel1 noise/triangle wave
              generation enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MAMP1</name>
              <description>DAC channel1 mask/amplitude
              selector</description>
              <bitOffset>8</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DMAEN1</name>
              <description>DAC channel1 DMA enable</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EN2</name>
              <description>DAC channel2 enable</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>BOFF2</name>
              <description>DAC channel2 output buffer
              disable</description>
              <bitOffset>17</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TEN2</name>
              <description>DAC channel2 trigger
              enable</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TSEL2</name>
              <description>DAC channel2 trigger
              selection</description>
              <bitOffset>19</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>WAVE2</name>
              <description>DAC channel2 noise/triangle wave
              generation enable</description>
              <bitOffset>22</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>MAMP2</name>
              <description>DAC channel2 mask/amplitude
              selector</description>
              <bitOffset>24</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DMAEN2</name>
              <description>DAC channel2 DMA enable</description>
              <bitOffset>28</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SWTRIGR</name>
          <displayName>SWTRIGR</displayName>
          <description>DAC software trigger register
          (DAC_SWTRIGR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SWTRIG1</name>
              <description>DAC channel1 software
              trigger</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWTRIG2</name>
              <description>DAC channel2 software
              trigger</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR12R1</name>
          <displayName>DHR12R1</displayName>
          <description>DAC channel1 12-bit right-aligned data
          holding register(DAC_DHR12R1)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC1DHR</name>
              <description>DAC channel1 12-bit right-aligned
              data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR12L1</name>
          <displayName>DHR12L1</displayName>
          <description>DAC channel1 12-bit left aligned data
          holding register (DAC_DHR12L1)</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC1DHR</name>
              <description>DAC channel1 12-bit left-aligned
              data</description>
              <bitOffset>4</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR8R1</name>
          <displayName>DHR8R1</displayName>
          <description>DAC channel1 8-bit right aligned data
          holding register (DAC_DHR8R1)</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC1DHR</name>
              <description>DAC channel1 8-bit right-aligned
              data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR12R2</name>
          <displayName>DHR12R2</displayName>
          <description>DAC channel2 12-bit right aligned data
          holding register (DAC_DHR12R2)</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC2DHR</name>
              <description>DAC channel2 12-bit right-aligned
              data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR12L2</name>
          <displayName>DHR12L2</displayName>
          <description>DAC channel2 12-bit left aligned data
          holding register (DAC_DHR12L2)</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC2DHR</name>
              <description>DAC channel2 12-bit left-aligned
              data</description>
              <bitOffset>4</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR8R2</name>
          <displayName>DHR8R2</displayName>
          <description>DAC channel2 8-bit right-aligned data
          holding register (DAC_DHR8R2)</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC2DHR</name>
              <description>DAC channel2 8-bit right-aligned
              data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR12RD</name>
          <displayName>DHR12RD</displayName>
          <description>Dual DAC 12-bit right-aligned data holding
          register (DAC_DHR12RD), Bits 31:28 Reserved, Bits 15:12
          Reserved</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC1DHR</name>
              <description>DAC channel1 12-bit right-aligned
              data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
            <field>
              <name>DACC2DHR</name>
              <description>DAC channel2 12-bit right-aligned
              data</description>
              <bitOffset>16</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR12LD</name>
          <displayName>DHR12LD</displayName>
          <description>DUAL DAC 12-bit left aligned data holding
          register (DAC_DHR12LD), Bits 19:16 Reserved, Bits 3:0
          Reserved</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC1DHR</name>
              <description>DAC channel1 12-bit left-aligned
              data</description>
              <bitOffset>4</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
            <field>
              <name>DACC2DHR</name>
              <description>DAC channel2 12-bit right-aligned
              data</description>
              <bitOffset>20</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DHR8RD</name>
          <displayName>DHR8RD</displayName>
          <description>DUAL DAC 8-bit right aligned data holding
          register (DAC_DHR8RD), Bits 31:16 Reserved</description>
          <addressOffset>0x28</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC1DHR</name>
              <description>DAC channel1 8-bit right-aligned
              data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>DACC2DHR</name>
              <description>DAC channel2 8-bit right-aligned
              data</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DOR1</name>
          <displayName>DOR1</displayName>
          <description>DAC channel1 data output register
          (DAC_DOR1)</description>
          <addressOffset>0x2C</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC1DOR</name>
              <description>DAC channel1 data output</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DOR2</name>
          <displayName>DOR2</displayName>
          <description>DAC channel2 data output register
          (DAC_DOR2)</description>
          <addressOffset>0x30</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DACC2DOR</name>
              <description>DAC channel2 data output</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>DBG</name>
      <description>Debug support</description>
      <groupName>DBG</groupName>
      <baseAddress>0xE0042000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>IDCODE</name>
          <displayName>IDCODE</displayName>
          <description>DBGMCU_IDCODE</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>DEV_ID</name>
              <description>DEV_ID</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
            <field>
              <name>REV_ID</name>
              <description>REV_ID</description>
              <bitOffset>16</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR</name>
          <displayName>CR</displayName>
          <description>DBGMCU_CR</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>DBG_SLEEP</name>
              <description>DBG_SLEEP</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_STOP</name>
              <description>DBG_STOP</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_STANDBY</name>
              <description>DBG_STANDBY</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TRACE_IOEN</name>
              <description>TRACE_IOEN</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TRACE_MODE</name>
              <description>TRACE_MODE</description>
              <bitOffset>6</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>DBG_IWDG_STOP</name>
              <description>DBG_IWDG_STOP</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_WWDG_STOP</name>
              <description>DBG_WWDG_STOP</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM1_STOP</name>
              <description>DBG_TIM1_STOP</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM2_STOP</name>
              <description>DBG_TIM2_STOP</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM3_STOP</name>
              <description>DBG_TIM3_STOP</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM4_STOP</name>
              <description>DBG_TIM4_STOP</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_I2C1_SMBUS_TIMEOUT</name>
              <description>DBG_I2C1_SMBUS_TIMEOUT</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_I2C2_SMBUS_TIMEOUT</name>
              <description>DBG_I2C2_SMBUS_TIMEOUT</description>
              <bitOffset>16</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM5_STOP</name>
              <description>DBG_TIM5_STOP</description>
              <bitOffset>18</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM6_STOP</name>
              <description>DBG_TIM6_STOP</description>
              <bitOffset>19</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM7_STOP</name>
              <description>DBG_TIM7_STOP</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM15_STOP</name>
              <description>TIM15 counter stopped when core is
              halted</description>
              <bitOffset>22</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM16_STOP</name>
              <description>TIM16 counter stopped when core is
              halted</description>
              <bitOffset>23</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM17_STOP</name>
              <description>TIM17 counter stopped when core is
              halted</description>
              <bitOffset>24</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM12_STOP</name>
              <description>TIM12 counter stopped when core is
              halted</description>
              <bitOffset>25</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM13_STOP</name>
              <description>TIM13 counter stopped when core is
              halted</description>
              <bitOffset>26</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DBG_TIM14_STOP</name>
              <description>TIM14 counter stopped when core is
              halted</description>
              <bitOffset>27</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>UART4</name>
      <description>Universal asynchronous receiver
      transmitter</description>
      <groupName>USART</groupName>
      <baseAddress>0x40004C00</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>DAC</name>
        <description>DAC interrupt</description>
        <value>21</value>
      </interrupt>
      <registers>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>UART4_SR</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>PE</name>
              <description>Parity error</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>FE</name>
              <description>Framing error</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>NE</name>
              <description>Noise error flag</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>ORE</name>
              <description>Overrun error</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>IDLE</name>
              <description>IDLE line detected</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>RXNE</name>
              <description>Read data register not
              empty</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TC</name>
              <description>Transmission complete</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TXE</name>
              <description>Transmit data register
              empty</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>LBD</name>
              <description>LIN break detection flag</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
        <register>
          <name>DR</name>
          <displayName>DR</displayName>
          <description>UART4_DR</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>DR</name>
              <description>DR</description>
              <bitOffset>0</bitOffset>
              <bitWidth>9</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BRR</name>
          <displayName>BRR</displayName>
          <description>UART4_BRR</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>DIV_Fraction</name>
              <description>DIV_Fraction</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DIV_Mantissa</name>
              <description>DIV_Mantissa</description>
              <bitOffset>4</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>UART4_CR1</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>SBK</name>
              <description>Send break</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RWU</name>
              <description>Receiver wakeup</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RE</name>
              <description>Receiver enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TE</name>
              <description>Transmitter enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDLEIE</name>
              <description>IDLE interrupt enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RXNEIE</name>
              <description>RXNE interrupt enable</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>Transmission complete interrupt
              enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TXEIE</name>
              <description>TXE interrupt enable</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PEIE</name>
              <description>PE interrupt enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PS</name>
              <description>Parity selection</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PCE</name>
              <description>Parity control enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAKE</name>
              <description>Wakeup method</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>M</name>
              <description>Word length</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UE</name>
              <description>USART enable</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>UART4_CR2</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>ADD</name>
              <description>Address of the USART node</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>LBDL</name>
              <description>lin break detection length</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LBDIE</name>
              <description>LIN break detection interrupt
              enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>STOP</name>
              <description>STOP bits</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>LINEN</name>
              <description>LIN mode enable</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR3</name>
          <displayName>CR3</displayName>
          <description>UART4_CR3</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>EIE</name>
              <description>Error interrupt enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IREN</name>
              <description>IrDA mode enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IRLP</name>
              <description>IrDA low-power</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HDSEL</name>
              <description>Half-duplex selection</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DMAR</name>
              <description>DMA enable receiver</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DMAT</name>
              <description>DMA enable transmitter</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>UART5</name>
      <description>Universal asynchronous receiver
      transmitter</description>
      <groupName>USART</groupName>
      <baseAddress>0x40005000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>UART4_SR</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>PE</name>
              <description>PE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>FE</name>
              <description>FE</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>NE</name>
              <description>NE</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>ORE</name>
              <description>ORE</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>IDLE</name>
              <description>IDLE</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>RXNE</name>
              <description>RXNE</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TC</name>
              <description>TC</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TXE</name>
              <description>TXE</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>LBD</name>
              <description>LBD</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
          </fields>
        </register>
        <register>
          <name>DR</name>
          <displayName>DR</displayName>
          <description>UART4_DR</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>DR</name>
              <description>DR</description>
              <bitOffset>0</bitOffset>
              <bitWidth>9</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>BRR</name>
          <displayName>BRR</displayName>
          <description>UART4_BRR</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>DIV_Fraction</name>
              <description>DIV_Fraction</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DIV_Mantissa</name>
              <description>DIV_Mantissa</description>
              <bitOffset>4</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>UART4_CR1</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>SBK</name>
              <description>SBK</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RWU</name>
              <description>RWU</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RE</name>
              <description>RE</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TE</name>
              <description>TE</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IDLEIE</name>
              <description>IDLEIE</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RXNEIE</name>
              <description>RXNEIE</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TCIE</name>
              <description>TCIE</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TXEIE</name>
              <description>TXEIE</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PEIE</name>
              <description>PEIE</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PS</name>
              <description>PS</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PCE</name>
              <description>PCE</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WAKE</name>
              <description>WAKE</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>M</name>
              <description>M</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>UE</name>
              <description>UE</description>
              <bitOffset>13</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>UART4_CR2</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>ADD</name>
              <description>ADD</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>LBDL</name>
              <description>LBDL</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LBDIE</name>
              <description>LBDIE</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>STOP</name>
              <description>STOP</description>
              <bitOffset>12</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>LINEN</name>
              <description>LINEN</description>
              <bitOffset>14</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR3</name>
          <displayName>CR3</displayName>
          <description>UART4_CR3</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x0</resetValue>
          <fields>
            <field>
              <name>EIE</name>
              <description>Error interrupt enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IREN</name>
              <description>IrDA mode enable</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>IRLP</name>
              <description>IrDA low-power</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>HDSEL</name>
              <description>Half-duplex selection</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DMAT</name>
              <description>DMA enable transmitter</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>CRC</name>
      <description>CRC calculation unit</description>
      <groupName>CRC</groupName>
      <baseAddress>0x40023000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>DR</name>
          <displayName>DR</displayName>
          <description>Data register</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0xFFFFFFFF</resetValue>
          <fields>
            <field>
              <name>DR</name>
              <description>Data Register</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IDR</name>
          <displayName>IDR</displayName>
          <description>Independent Data register</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IDR</name>
              <description>Independent Data register</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR</name>
          <displayName>CR</displayName>
          <description>Control register</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>RESET</name>
              <description>Reset bit</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>FLASH</name>
      <description>FLASH</description>
      <groupName>FLASH</groupName>
      <baseAddress>0x40022000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <registers>
        <register>
          <name>ACR</name>
          <displayName>ACR</displayName>
          <description>Flash access control register</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000030</resetValue>
          <fields>
            <field>
              <name>LATENCY</name>
              <description>Latency</description>
              <bitOffset>0</bitOffset>
              <bitWidth>3</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>HLFCYA</name>
              <description>Flash half cycle access
              enable</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PRFTBE</name>
              <description>Prefetch buffer enable</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PRFTBS</name>
              <description>Prefetch buffer status</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>KEYR</name>
          <displayName>KEYR</displayName>
          <description>Flash key register</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>KEY</name>
              <description>FPEC key</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>OPTKEYR</name>
          <displayName>OPTKEYR</displayName>
          <description>Flash option key register</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>OPTKEY</name>
              <description>Option byte key</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>Status register</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>EOP</name>
              <description>End of operation</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>WRPRTERR</name>
              <description>Write protection error</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>PGERR</name>
              <description>Programming error</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>BSY</name>
              <description>Busy</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
        <register>
          <name>CR</name>
          <displayName>CR</displayName>
          <description>Control register</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000080</resetValue>
          <fields>
            <field>
              <name>PG</name>
              <description>Programming</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>PER</name>
              <description>Page Erase</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>MER</name>
              <description>Mass Erase</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OPTPG</name>
              <description>Option byte programming</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OPTER</name>
              <description>Option byte erase</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>STRT</name>
              <description>Start</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>LOCK</name>
              <description>Lock</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>OPTWRE</name>
              <description>Option bytes write enable</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ERRIE</name>
              <description>Error interrupt enable</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EOPIE</name>
              <description>End of operation interrupt
              enable</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>AR</name>
          <displayName>AR</displayName>
          <description>Flash address register</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>FAR</name>
              <description>Flash Address</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>OBR</name>
          <displayName>OBR</displayName>
          <description>Option byte register</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x03FFFFFC</resetValue>
          <fields>
            <field>
              <name>OPTERR</name>
              <description>Option byte error</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RDPRT</name>
              <description>Read protection</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>WDG_SW</name>
              <description>WDG_SW</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>nRST_STOP</name>
              <description>nRST_STOP</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>nRST_STDBY</name>
              <description>nRST_STDBY</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>Data0</name>
              <description>Data0</description>
              <bitOffset>10</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>Data1</name>
              <description>Data1</description>
              <bitOffset>18</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>WRPR</name>
          <displayName>WRPR</displayName>
          <description>Write protection register</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0xFFFFFFFF</resetValue>
          <fields>
            <field>
              <name>WRP</name>
              <description>Write protect</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>BKP</name>
      <description>Backup registers</description>
      <groupName>BKP</groupName>
      <baseAddress>0x40006C04</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>FLASH</name>
        <description>Flash global interrupt</description>
        <value>4</value>
      </interrupt>
      <registers>
        <register>
          <name>DR1</name>
          <displayName>DR1</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D1</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR2</name>
          <displayName>DR2</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D2</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR3</name>
          <displayName>DR3</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D3</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR4</name>
          <displayName>DR4</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D4</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR5</name>
          <displayName>DR5</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D5</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR6</name>
          <displayName>DR6</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D6</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR7</name>
          <displayName>DR7</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D7</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR8</name>
          <displayName>DR8</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D8</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR9</name>
          <displayName>DR9</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D9</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR10</name>
          <displayName>DR10</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D10</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR11</name>
          <displayName>DR11</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x3C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DR11</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR12</name>
          <displayName>DR12</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x40</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DR12</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR13</name>
          <displayName>DR13</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x44</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DR13</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR14</name>
          <displayName>DR14</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x48</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D14</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR15</name>
          <displayName>DR15</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x4C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D15</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR16</name>
          <displayName>DR16</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x50</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D16</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR17</name>
          <displayName>DR17</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x54</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D17</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR18</name>
          <displayName>DR18</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x58</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D18</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR19</name>
          <displayName>DR19</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x5C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D19</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR20</name>
          <displayName>DR20</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x60</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D20</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR21</name>
          <displayName>DR21</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x64</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D21</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR22</name>
          <displayName>DR22</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x68</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D22</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR23</name>
          <displayName>DR23</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x6C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D23</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR24</name>
          <displayName>DR24</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x70</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D24</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR25</name>
          <displayName>DR25</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x74</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D25</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR26</name>
          <displayName>DR26</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x78</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D26</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR27</name>
          <displayName>DR27</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x7C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D27</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR28</name>
          <displayName>DR28</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x80</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D28</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR29</name>
          <displayName>DR29</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x84</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D29</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR30</name>
          <displayName>DR30</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x88</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D30</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR31</name>
          <displayName>DR31</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x8C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D31</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR32</name>
          <displayName>DR32</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x90</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D32</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR33</name>
          <displayName>DR33</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x94</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D33</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR34</name>
          <displayName>DR34</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x98</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D34</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR35</name>
          <displayName>DR35</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0x9C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D35</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR36</name>
          <displayName>DR36</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0xA0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D36</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR37</name>
          <displayName>DR37</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0xA4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D37</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR38</name>
          <displayName>DR38</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0xA8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D38</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR39</name>
          <displayName>DR39</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0xAC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D39</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR40</name>
          <displayName>DR40</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0xB0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D40</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR41</name>
          <displayName>DR41</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0xB4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D41</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR42</name>
          <displayName>DR42</displayName>
          <description>Backup data register (BKP_DR)</description>
          <addressOffset>0xB8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>D42</name>
              <description>Backup data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>RTCCR</name>
          <displayName>RTCCR</displayName>
          <description>RTC clock calibration register
          (BKP_RTCCR)</description>
          <addressOffset>0x28</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CAL</name>
              <description>Calibration value</description>
              <bitOffset>0</bitOffset>
              <bitWidth>7</bitWidth>
            </field>
            <field>
              <name>CCO</name>
              <description>Calibration Clock Output</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ASOE</name>
              <description>Alarm or second output
              enable</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ASOS</name>
              <description>Alarm or second output
              selection</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR</name>
          <displayName>CR</displayName>
          <description>Backup control register
          (BKP_CR)</description>
          <addressOffset>0x2C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>TPE</name>
              <description>Tamper pin enable</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>TPAL</name>
              <description>Tamper pin active level</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CSR</name>
          <displayName>CSR</displayName>
          <description>BKP_CSR control/status register
          (BKP_CSR)</description>
          <addressOffset>0x30</addressOffset>
          <size>0x20</size>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CTE</name>
              <description>Clear Tamper event</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
              <access>write-only</access>
            </field>
            <field>
              <name>CTI</name>
              <description>Clear Tamper Interrupt</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
              <access>write-only</access>
            </field>
            <field>
              <name>TPIE</name>
              <description>Tamper Pin interrupt
              enable</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-write</access>
            </field>
            <field>
              <name>TEF</name>
              <description>Tamper Event Flag</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
            <field>
              <name>TIF</name>
              <description>Tamper Interrupt Flag</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
              <access>read-only</access>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>ADC1</name>
      <description>Analog to digital converter</description>
      <groupName>ADC</groupName>
      <baseAddress>0x40012400</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x400</size>
        <usage>registers</usage>
      </addressBlock>
      <interrupt>
        <name>ADC</name>
        <description>ADC1 global interrupt</description>
        <value>18</value>
      </interrupt>
      <registers>
        <register>
          <name>SR</name>
          <displayName>SR</displayName>
          <description>status register</description>
          <addressOffset>0x0</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>STRT</name>
              <description>Regular channel start flag</description>
              <bitOffset>4</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>JSTRT</name>
              <description>Injected channel start
              flag</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>JEOC</name>
              <description>Injected channel end of
              conversion</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EOC</name>
              <description>Regular channel end of
              conversion</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>AWD</name>
              <description>Analog watchdog flag</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR1</name>
          <displayName>CR1</displayName>
          <description>control register 1</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>AWDEN</name>
              <description>Analog watchdog enable on regular
              channels</description>
              <bitOffset>23</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>JAWDEN</name>
              <description>Analog watchdog enable on injected
              channels</description>
              <bitOffset>22</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DUALMOD</name>
              <description>Dual mode selection</description>
              <bitOffset>16</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>DISCNUM</name>
              <description>Discontinuous mode channel
              count</description>
              <bitOffset>13</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>JDISCEN</name>
              <description>Discontinuous mode on injected
              channels</description>
              <bitOffset>12</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DISCEN</name>
              <description>Discontinuous mode on regular
              channels</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>JAUTO</name>
              <description>Automatic injected group
              conversion</description>
              <bitOffset>10</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>AWDSGL</name>
              <description>Enable the watchdog on a single channel
              in scan mode</description>
              <bitOffset>9</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SCAN</name>
              <description>Scan mode</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>JEOCIE</name>
              <description>Interrupt enable for injected
              channels</description>
              <bitOffset>7</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>AWDIE</name>
              <description>Analog watchdog interrupt
              enable</description>
              <bitOffset>6</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EOCIE</name>
              <description>Interrupt enable for EOC</description>
              <bitOffset>5</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>AWDCH</name>
              <description>Analog watchdog channel select
              bits</description>
              <bitOffset>0</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>CR2</name>
          <displayName>CR2</displayName>
          <description>control register 2</description>
          <addressOffset>0x8</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>TSVREFE</name>
              <description>Temperature sensor and VREFINT
              enable</description>
              <bitOffset>23</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>SWSTART</name>
              <description>Start conversion of regular
              channels</description>
              <bitOffset>22</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>JSWSTART</name>
              <description>Start conversion of injected
              channels</description>
              <bitOffset>21</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EXTTRIG</name>
              <description>External trigger conversion mode for
              regular channels</description>
              <bitOffset>20</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>EXTSEL</name>
              <description>External event select for regular
              group</description>
              <bitOffset>17</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>JEXTTRIG</name>
              <description>External trigger conversion mode for
              injected channels</description>
              <bitOffset>15</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>JEXTSEL</name>
              <description>External event select for injected
              group</description>
              <bitOffset>12</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>ALIGN</name>
              <description>Data alignment</description>
              <bitOffset>11</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>DMA</name>
              <description>Direct memory access mode</description>
              <bitOffset>8</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>RSTCAL</name>
              <description>Reset calibration</description>
              <bitOffset>3</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CAL</name>
              <description>A/D calibration</description>
              <bitOffset>2</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>CONT</name>
              <description>Continuous conversion</description>
              <bitOffset>1</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
            <field>
              <name>ADON</name>
              <description>A/D converter ON / OFF</description>
              <bitOffset>0</bitOffset>
              <bitWidth>1</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SMPR1</name>
          <displayName>SMPR1</displayName>
          <description>sample time register 1</description>
          <addressOffset>0xC</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SMP10</name>
              <description>Channel 10 sample time
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP11</name>
              <description>Channel 11 sample time
              selection</description>
              <bitOffset>3</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP12</name>
              <description>Channel 12 sample time
              selection</description>
              <bitOffset>6</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP13</name>
              <description>Channel 13 sample time
              selection</description>
              <bitOffset>9</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP14</name>
              <description>Channel 14 sample time
              selection</description>
              <bitOffset>12</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP15</name>
              <description>Channel 15 sample time
              selection</description>
              <bitOffset>15</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP16</name>
              <description>Channel 16 sample time
              selection</description>
              <bitOffset>18</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP17</name>
              <description>Channel 17 sample time
              selection</description>
              <bitOffset>21</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SMPR2</name>
          <displayName>SMPR2</displayName>
          <description>sample time register 2</description>
          <addressOffset>0x10</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SMP0</name>
              <description>Channel 0 sample time
              selection</description>
              <bitOffset>0</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP1</name>
              <description>Channel 1 sample time
              selection</description>
              <bitOffset>3</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP2</name>
              <description>Channel 2 sample time
              selection</description>
              <bitOffset>6</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP3</name>
              <description>Channel 3 sample time
              selection</description>
              <bitOffset>9</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP4</name>
              <description>Channel 4 sample time
              selection</description>
              <bitOffset>12</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP5</name>
              <description>Channel 5 sample time
              selection</description>
              <bitOffset>15</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP6</name>
              <description>Channel 6 sample time
              selection</description>
              <bitOffset>18</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP7</name>
              <description>Channel 7 sample time
              selection</description>
              <bitOffset>21</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP8</name>
              <description>Channel 8 sample time
              selection</description>
              <bitOffset>24</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
            <field>
              <name>SMP9</name>
              <description>Channel 9 sample time
              selection</description>
              <bitOffset>27</bitOffset>
              <bitWidth>3</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JOFR1</name>
          <displayName>JOFR1</displayName>
          <description>injected channel data offset register
          x</description>
          <addressOffset>0x14</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JOFFSET1</name>
              <description>Data offset for injected channel
              x</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JOFR2</name>
          <displayName>JOFR2</displayName>
          <description>injected channel data offset register
          x</description>
          <addressOffset>0x18</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JOFFSET2</name>
              <description>Data offset for injected channel
              x</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JOFR3</name>
          <displayName>JOFR3</displayName>
          <description>injected channel data offset register
          x</description>
          <addressOffset>0x1C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JOFFSET3</name>
              <description>Data offset for injected channel
              x</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JOFR4</name>
          <displayName>JOFR4</displayName>
          <description>injected channel data offset register
          x</description>
          <addressOffset>0x20</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JOFFSET4</name>
              <description>Data offset for injected channel
              x</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>HTR</name>
          <displayName>HTR</displayName>
          <description>watchdog higher threshold
          register</description>
          <addressOffset>0x24</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000FFF</resetValue>
          <fields>
            <field>
              <name>HT</name>
              <description>Analog watchdog higher
              threshold</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>LTR</name>
          <displayName>LTR</displayName>
          <description>watchdog lower threshold
          register</description>
          <addressOffset>0x28</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>LT</name>
              <description>Analog watchdog lower
              threshold</description>
              <bitOffset>0</bitOffset>
              <bitWidth>12</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SQR1</name>
          <displayName>SQR1</displayName>
          <description>regular sequence register 1</description>
          <addressOffset>0x2C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>L</name>
              <description>Regular channel sequence
              length</description>
              <bitOffset>20</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
            <field>
              <name>SQ16</name>
              <description>16th conversion in regular
              sequence</description>
              <bitOffset>15</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ15</name>
              <description>15th conversion in regular
              sequence</description>
              <bitOffset>10</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ14</name>
              <description>14th conversion in regular
              sequence</description>
              <bitOffset>5</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ13</name>
              <description>13th conversion in regular
              sequence</description>
              <bitOffset>0</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SQR2</name>
          <displayName>SQR2</displayName>
          <description>regular sequence register 2</description>
          <addressOffset>0x30</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SQ12</name>
              <description>12th conversion in regular
              sequence</description>
              <bitOffset>25</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ11</name>
              <description>11th conversion in regular
              sequence</description>
              <bitOffset>20</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ10</name>
              <description>10th conversion in regular
              sequence</description>
              <bitOffset>15</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ9</name>
              <description>9th conversion in regular
              sequence</description>
              <bitOffset>10</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ8</name>
              <description>8th conversion in regular
              sequence</description>
              <bitOffset>5</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ7</name>
              <description>7th conversion in regular
              sequence</description>
              <bitOffset>0</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>SQR3</name>
          <displayName>SQR3</displayName>
          <description>regular sequence register 3</description>
          <addressOffset>0x34</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SQ6</name>
              <description>6th conversion in regular
              sequence</description>
              <bitOffset>25</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ5</name>
              <description>5th conversion in regular
              sequence</description>
              <bitOffset>20</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ4</name>
              <description>4th conversion in regular
              sequence</description>
              <bitOffset>15</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ3</name>
              <description>3rd conversion in regular
              sequence</description>
              <bitOffset>10</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ2</name>
              <description>2nd conversion in regular
              sequence</description>
              <bitOffset>5</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>SQ1</name>
              <description>1st conversion in regular
              sequence</description>
              <bitOffset>0</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JSQR</name>
          <displayName>JSQR</displayName>
          <description>injected sequence register</description>
          <addressOffset>0x38</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JL</name>
              <description>Injected sequence length</description>
              <bitOffset>20</bitOffset>
              <bitWidth>2</bitWidth>
            </field>
            <field>
              <name>JSQ4</name>
              <description>4th conversion in injected
              sequence</description>
              <bitOffset>15</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>JSQ3</name>
              <description>3rd conversion in injected
              sequence</description>
              <bitOffset>10</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>JSQ2</name>
              <description>2nd conversion in injected
              sequence</description>
              <bitOffset>5</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
            <field>
              <name>JSQ1</name>
              <description>1st conversion in injected
              sequence</description>
              <bitOffset>0</bitOffset>
              <bitWidth>5</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JDR1</name>
          <displayName>JDR1</displayName>
          <description>injected data register x</description>
          <addressOffset>0x3C</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JDATA</name>
              <description>Injected data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JDR2</name>
          <displayName>JDR2</displayName>
          <description>injected data register x</description>
          <addressOffset>0x40</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JDATA</name>
              <description>Injected data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JDR3</name>
          <displayName>JDR3</displayName>
          <description>injected data register x</description>
          <addressOffset>0x44</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JDATA</name>
              <description>Injected data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>JDR4</name>
          <displayName>JDR4</displayName>
          <description>injected data register x</description>
          <addressOffset>0x48</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>JDATA</name>
              <description>Injected data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>DR</name>
          <displayName>DR</displayName>
          <description>regular data register</description>
          <addressOffset>0x4C</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>DATA</name>
              <description>Regular data</description>
              <bitOffset>0</bitOffset>
              <bitWidth>16</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>
    <peripheral>
      <name>NVIC</name>
      <description>Nested Vectored Interrupt
      Controller</description>
      <groupName>NVIC</groupName>
      <baseAddress>0xE000E000</baseAddress>
      <addressBlock>
        <offset>0x0</offset>
        <size>0x1001</size>
        <usage>registers</usage>
      </addressBlock>
      <addressBlock>
        <offset>0x1001</offset>
        <size>0xFFFFF3FF</size>
        <usage>reserved</usage>
      </addressBlock>
      <registers>
        <register>
          <name>ICTR</name>
          <displayName>ICTR</displayName>
          <description>Interrupt Controller Type
          Register</description>
          <addressOffset>0x4</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>INTLINESNUM</name>
              <description>Total number of interrupt lines in
              groups</description>
              <bitOffset>0</bitOffset>
              <bitWidth>4</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>STIR</name>
          <displayName>STIR</displayName>
          <description>Software Triggered Interrupt
          Register</description>
          <addressOffset>0xF00</addressOffset>
          <size>0x20</size>
          <access>write-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>INTID</name>
              <description>interrupt to be triggered</description>
              <bitOffset>0</bitOffset>
              <bitWidth>9</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ISER0</name>
          <displayName>ISER0</displayName>
          <description>Interrupt Set-Enable Register</description>
          <addressOffset>0x100</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SETENA</name>
              <description>SETENA</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ISER1</name>
          <displayName>ISER1</displayName>
          <description>Interrupt Set-Enable Register</description>
          <addressOffset>0x104</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SETENA</name>
              <description>SETENA</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ICER0</name>
          <displayName>ICER0</displayName>
          <description>Interrupt Clear-Enable
          Register</description>
          <addressOffset>0x180</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CLRENA</name>
              <description>CLRENA</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ICER1</name>
          <displayName>ICER1</displayName>
          <description>Interrupt Clear-Enable
          Register</description>
          <addressOffset>0x184</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CLRENA</name>
              <description>CLRENA</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ISPR0</name>
          <displayName>ISPR0</displayName>
          <description>Interrupt Set-Pending Register</description>
          <addressOffset>0x200</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SETPEND</name>
              <description>SETPEND</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ISPR1</name>
          <displayName>ISPR1</displayName>
          <description>Interrupt Set-Pending Register</description>
          <addressOffset>0x204</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>SETPEND</name>
              <description>SETPEND</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ICPR0</name>
          <displayName>ICPR0</displayName>
          <description>Interrupt Clear-Pending
          Register</description>
          <addressOffset>0x280</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CLRPEND</name>
              <description>CLRPEND</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>ICPR1</name>
          <displayName>ICPR1</displayName>
          <description>Interrupt Clear-Pending
          Register</description>
          <addressOffset>0x284</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>CLRPEND</name>
              <description>CLRPEND</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IABR0</name>
          <displayName>IABR0</displayName>
          <description>Interrupt Active Bit Register</description>
          <addressOffset>0x300</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ACTIVE</name>
              <description>ACTIVE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IABR1</name>
          <displayName>IABR1</displayName>
          <description>Interrupt Active Bit Register</description>
          <addressOffset>0x304</addressOffset>
          <size>0x20</size>
          <access>read-only</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>ACTIVE</name>
              <description>ACTIVE</description>
              <bitOffset>0</bitOffset>
              <bitWidth>32</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR0</name>
          <displayName>IPR0</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x400</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR1</name>
          <displayName>IPR1</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x404</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR2</name>
          <displayName>IPR2</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x408</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR3</name>
          <displayName>IPR3</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x40C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR4</name>
          <displayName>IPR4</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x410</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR5</name>
          <displayName>IPR5</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x414</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR6</name>
          <displayName>IPR6</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x418</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR7</name>
          <displayName>IPR7</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x41C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR8</name>
          <displayName>IPR8</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x420</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR9</name>
          <displayName>IPR9</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x424</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR10</name>
          <displayName>IPR10</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x428</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR11</name>
          <displayName>IPR11</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x42C</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR12</name>
          <displayName>IPR12</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x430</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
        <register>
          <name>IPR13</name>
          <displayName>IPR13</displayName>
          <description>Interrupt Priority Register</description>
          <addressOffset>0x434</addressOffset>
          <size>0x20</size>
          <access>read-write</access>
          <resetValue>0x00000000</resetValue>
          <fields>
            <field>
              <name>IPR_N0</name>
              <description>IPR_N0</description>
              <bitOffset>0</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N1</name>
              <description>IPR_N1</description>
              <bitOffset>8</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N2</name>
              <description>IPR_N2</description>
              <bitOffset>16</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
            <field>
              <name>IPR_N3</name>
              <description>IPR_N3</description>
              <bitOffset>24</bitOffset>
              <bitWidth>8</bitWidth>
            </field>
          </fields>
        </register>
      </registers>
    </peripheral>  
  </peripherals>
</device>
