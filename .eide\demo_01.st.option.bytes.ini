﻿##############################################
#
#           STM32 Option Bytes
#
#   Usage: Uncomment to enable options
#
##############################################

# RDP = <Level> 
# BOR_LEV = <Level>

# WWDG_SW = <Value> 
# IWDG_SW = <Value>
# IWDG_STOP = <Value> 
# IWDG_STDBY = <Value>
# IWDG_ULP = <Value> 

# FZ_IWDG_STOP = <Value>
# FZ_IWDG_STDBY = <Value>

# nRST_STOP = <Value> 
# nRST_STDBY = <Value>

# nBOOT_SEL = <Value> 
# nRST_SHDW = <Value> 
# PCROP_RDP = <Value>

# nBFB2 = <Value> 
# BFB2 = <Value>

# nBoot1 = <Value> 
# Boot1 = <Value>
# nBoot0 = <Value> 
# nBoot0_SW_Cfg = <Value>

# VDDA = <Value> 
# SDADC12_VDD = <Value>

# DB1M = <Value> 
# DUALBANK = <Value>
# nDBANK = <Value> 

# BOOT0_nSW_Config = <Value>
# Data0 = <Value> 
# Data1 = <Value>

# nSRAM_Parity = <Value> 
# SRAM2_RST = <Value>
# SRAM2_PE = <Value> 

# DDS = <Value>
# FSD = <Value> 
# SFSA = <Value>
# C2OPT = <Value> 
# NBRSD = <Value>
# SNBRSA = <Value> 
# SBRSA = <Value>
# BRSD = <Value> 
# SBRV = <Value>

# DMEPB = <Value> 
# DMESB = <Value> 

# Security = <Value> 
# CM7_BOOT_ADD0 = <Value>
# CM7_BOOT_ADD1 = <Value>

# IWDG1 = <Value>
# IWDG2 = <Value> 

# nRST_STDBY_D2 = <Value>
# BOOT_CM4 = <Value> 

# nRST_STDBY_D1 = <Value>
# BOOT_CM7 = <Value> 

# CM7_BOOT_ADD0 = <Value>
# CM7_BOOT_ADD1 = <Value>

# DMEPA = <Value> 
# DMESA = <Value> 

# SECA_strt = <Value>
# SECA_end = <Value> 
# SECB_strt = <Value>
# SECB_end = <Value> 

# DTCM_RAM = <Value>
# SPRMOD = <Value> 
# WPRMOD = <Value>

# PCROPA_STRT = <Value> 
# PCROPA_END = <Value>
# PCROPB_STRT = <Value> 
# PCROPB_END = <Value>

# WRP = <Value> 
# WRP2 = <Value>
# WRP3 = <Value> 
# WRP4 = <Value>
# WRP1A_STRT = <Value> 
# WRP1A_END = <Value>
# WRP1B_STRT = <Value> 
# WRP1B_END = <Value>
# WRP2A_STRT = <Value> 
# WRP2A_END = <Value>
# WRP2B_STRT = <Value> 
# WRP2B_END = <Value>

# IPCCDBA = <Value>