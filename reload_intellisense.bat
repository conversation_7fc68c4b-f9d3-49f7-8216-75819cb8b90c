@echo off
echo 正在重新加载VSCode IntelliSense...

REM 删除IntelliSense缓存
if exist ".vscode\browse.vc.db" (
    echo 删除旧的IntelliSense数据库...
    del ".vscode\browse.vc.db"
)

if exist ".vscode\ipch" (
    echo 删除IntelliSense缓存目录...
    rmdir /s /q ".vscode\ipch"
)

echo.
echo ========================================
echo IntelliSense缓存已清除
echo 请按照以下步骤操作：
echo.
echo 1. 重新启动VSCode
echo 2. 按 Ctrl+Shift+P
echo 3. 输入 "C/C++: Reset IntelliSense Database"
echo 4. 等待重新索引完成
echo 5. 测试智能提示功能
echo ========================================
echo.
pause
