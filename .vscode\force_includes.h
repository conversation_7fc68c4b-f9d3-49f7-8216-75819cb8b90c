// VSCode强制包含文件 - 提供完整STM32智能提示
#ifndef __FORCE_INCLUDES_H
#define __FORCE_INCLUDES_H

// 核心宏定义
#define USE_STDPERIPH_DRIVER
#define STM32F10X_MD
#define __CC_ARM
#define __arm__

// 标准C库 - 确保所有基本类型有智能提示
#include <stdint.h>    // uint8_t, uint16_t, uint32_t, int8_t, int16_t, int32_t
#include <stdio.h>     // printf, scanf等标准输入输出
#include <stdarg.h>    // 可变参数列表
#include <string.h>    // strlen, strcpy等字符串函数
#include <stdlib.h>    // malloc, free等内存管理
#include <stdbool.h>   // bool, true, false

// STM32核心头文件
#include "stm32f10x.h"
#include "core_cm3.h"

// STM32外设库头文件 - 按字母顺序
#include "stm32f10x_adc.h"
#include "stm32f10x_bkp.h"
#include "stm32f10x_can.h"
#include "stm32f10x_crc.h"
#include "stm32f10x_dac.h"
#include "stm32f10x_dbgmcu.h"
#include "stm32f10x_dma.h"
#include "stm32f10x_exti.h"
#include "stm32f10x_flash.h"
#include "stm32f10x_fsmc.h"
#include "stm32f10x_gpio.h"
#include "stm32f10x_i2c.h"
#include "stm32f10x_iwdg.h"
#include "stm32f10x_pwr.h"
#include "stm32f10x_rcc.h"
#include "stm32f10x_rtc.h"
#include "stm32f10x_sdio.h"
#include "stm32f10x_spi.h"
#include "stm32f10x_tim.h"
#include "stm32f10x_usart.h"
#include "stm32f10x_wwdg.h"
#include "misc.h"

// 常用宏定义
#ifndef ENABLE
#define ENABLE  ((FunctionalState)1)
#endif

#ifndef DISABLE
#define DISABLE ((FunctionalState)0)
#endif

#ifndef SET
#define SET     ((FlagStatus)1)
#endif

#ifndef RESET
#define RESET   ((FlagStatus)0)
#endif

#endif // __FORCE_INCLUDES_H
