// File: STM32F100_DBGMCU.ini
// Version: 1.0.0
// Note: refer to STM32F100xx Reference manual (RM0041)
//                STM32F100xx datasheets


/* variable to hold register values */
define unsigned long DbgMCU_CR;


// <<< Use Configuration Wizard in Context Menu >>>

// <h> Debug MCU configuration register (DBGMCU_CR)
//                                   <i> Reserved bits must be kept at reset value
//   <o.27> DBG_TIM14_STOP           <i> TIM14 counter stopped when core is halted
//   <o.26> DBG_TIM13_STOP           <i> TIM13 counter stopped when core is halted
//   <o.25> DBG_TIM12_STOP           <i> TIM12 counter stopped when core is halted
//   <o.24> DBG_TIM17_STOP           <i> TIM17 counter stopped when core is halted
//   <o.23> DBG_TIM16_STOP           <i> TIM16 counter stopped when core is halted
//   <o.22> DBG_TIM15_STOP           <i> TIM15 counter stopped when core is halted
//   <o.20> DBG_TIM7_STOP            <i> TIM7 counter stopped when core is halted
//   <o.19> DBG_TIM6_STOP            <i> TIM6 counter stopped when core is halted
//   <o.18> DBG_TIM5_STOP            <i> TIM5 counter stopped when core is halted
//   <o.16> DBG_I2C2_SMBUS_TIMEOUT   <i> SMBUS timeout mode stopped when core is halted
//   <o.15> DBG_I2C1_SMBUS_TIMEOUT   <i> SMBUS timeout mode stopped when core is halted
//   <o.13> DBG_TIM4_STOP            <i> TIM4 counter stopped when core is halted
//   <o.12> DBG_TIM3_STOP            <i> TIM3 counter stopped when core is halted
//   <o.11> DBG_TIM2_STOP            <i> TIM2 counter stopped when core is halted
//   <o.10> DBG_TIM1_STOP            <i> TIM1 counter stopped when core is halted
//   <o.9>  DBG_WWDG_STOP            <i> Debug window watchdog stopped when core is halted
//   <o.8>  DBG_IWDG_STOP            <i> Debug independent watchdog stopped when core is halted
//   <o.6..7> TRACE_MODE             <i> Trace mode
//      <0=> Asynchronous
//      <1=> Synchronous: TRACEDATA Size 1
//      <2=> Synchronous: TRACEDATA Size 2
//      <3=> Synchronous: TRACEDATA Size 4
//   <o.5>  TRACE_IOEN               <i> Trace I/O enable
//   <o.2>  DBG_STANDBY              <i> Debug standby mode
//   <o.1>  DBG_STOP                 <i> Debug stop mode
//   <o.0>  DBG_SLEEP                <i> Debug sleep mode
// </h>
DbgMCU_CR = 0x00000007;

// <h> TPIU Pin Routing
//   <i> TRACECLK: Pin PE2
//   <i> TRACED[0]: Pin PE3
//   <i> TRACED[1]: Pin PE4
//   <i> TRACED[2]: Pin PE5
//   <i> TRACED[3]: Pin PE6
// </h>

// <<< end of configuration section >>>


/*----------------------------------------------------------------------------
  Setup_TracePins()  configure the used trace pins
 *----------------------------------------------------------------------------*/
FUNC void Setup_TracePins (unsigned char trace_mode) {

  /* trace pins are configuration not necessary */
}

/*----------------------------------------------------------------------------
  Setup_DBGMCU()  configure DBGMCU registers
 *----------------------------------------------------------------------------*/
FUNC void Setup_DBGMCU (void) {

  if (DbgMCU_CR & (1 << 5)){
    Setup_TracePins (((DbgMCU_CR >> 6) & 3));
  }

  _WDWORD(0xE0042004, DbgMCU_CR);                         // Set DBGMCU_CR
}


/*----------------------------------------------------------------------------
  OnResetExec()  Executed after reset via uVision's 'Reset'-button
 *----------------------------------------------------------------------------*/
FUNC void OnResetExec (void) {
  Setup_DBGMCU();
}

Setup_DBGMCU();                                           // Debugger Setup
