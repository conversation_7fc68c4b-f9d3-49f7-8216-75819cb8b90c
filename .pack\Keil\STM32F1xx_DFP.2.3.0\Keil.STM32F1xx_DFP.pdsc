<?xml version="1.0" encoding="UTF-8"?>

<package schemaVersion="1.4.0" xmlns:xs="http://www.w3.org/2001/XMLSchema-instance" xs:noNamespaceSchemaLocation="PACK.xsd">
  <vendor>Keil</vendor>
  <url>http://www.keil.com/pack/</url>
  <name>STM32F1xx_DFP</name>
  <description>STMicroelectronics STM32F1 Series Device Support, Drivers and Examples</description>

  <releases>
    <release version="2.3.0" date="2018-11-05">
      Added DBGMCU INI files for setting up debug configuration.
      Fixed case insensitive include of device header file (fixing build error on Linux).
      Updated CMSIS driver:
      - CAN:
      -- Corrected MessageSend function to only access required data for sending
      -- Corrected abort message send functionality
      -- Corrected SetBitrate function
      - EMAC: 
      -- Corrected ETH DMA initialization. Now done when MAC transmitter or receiver is enabled (resolving netInitialize/netUnnitialize/netInitialize issue).
      - USB Host and Device :
      -- Added support for CMSIS-RTOS2
      - USART:
      -- Corrected ARM_USART_SET_IRDA_PULSE control
      Updated Board drivers:
      - Updated Board Support LED_*.c files.
      Updated Examples:
      - Updated emWin examples to emWin V5.46e.
      - Updated USB Host examples thread stack settings
    </release>
    <release version="2.2.0" date="2017-04-21">
      Updated STM32F10x device header file
      Updated CMSIS driver:
      - CAN:
      -- Corrected filter setting for adding/removing maskable Standard ID
      -- Corrected clearing of overrun flag in interrupt routine
      -- Corrected receive overrun signaling
      -  Corrected CAN2 initialization was disabling CAN1 filters
      - USB Device:
      -- Corrected resume event signaling
      -- Corrected initial resume signaling after USB Bus Reset
      -- Corrected device status information
      -- VBUS sensing enabled by default
      - USB Host: Corrected over-current pin configuration
      - SPI: Added "Not Used" support for SPI MOSI and MISO pins
      Updated Board drivers:
      - MCBSTM32C Touch_STMPE811.c: Corrected occasional incorrect coordinate reading, when touch screen is released
      - GPIO: Corrected corruption of Serial wire JTAG pins alternate function configuration
      Updated examples:
      - Updated emWin examples to emWin V5.36f
      - Updated USB Device CDC ACM VirtualCOM examples (corrected initial UART receive size)
    </release>
    <release version="2.1.0" date="2016-04-14">
      Updated CMSIS drivers:
      - EMAC:
      -- Improved robustness by function checking that driver is powered (EMAC_FLAG_POWER)
      - CAN:
      -- Corrected functionality when only one CAN controller is used
      -- Corrected pin remap configuration for CAN2 port pins
      -- Corrected functionality when NULL pointer is provided for one or both signal callbacks in Initialize function
      - USB Device:
      -- Corrected Isochronous transfer
      -- Corrected IN Endpoint FIFO flush procedure
      - USB Host:
      -- Removed interrupt priority handling
      -- Corrected multiple packet sending
      -- Corrected PowerControl function for unconditional Power Off
      - USART:
      -- Corrected Peripheral Reset and Clock enable/disable (check if peripheral is available on selected device)
      -- Corrected CTS handling and added signal CTS change event.
      - SPI:
      -- Corrected Peripheral Reset and Clock enable/disable (check if peripheral is available on selected device)
      -- Corrected Bus Speed configuration
      -- Corrected 8bit/16bit Data register access, regarding the Data frame size
      -I2C:
      -- Corrected invalid __I2C_DMA field in I2C_DMA_TxEvent and I2C_DMA_RxEvent functions
      Updated/added examples:
      - Added examples using Network DualStack (IPv4/IPv6) Middleware targeting MCBSTM32C (Keil.MDK-Middleware.7.0.0.pack required)
      - Updated emWin examples to emWin V5.32
      - Updated CAN examples
      - Updated USB Host examples
      - Updated USB Device CDC ACM VirtualCOM examples
      Updated RTE_Device.h: Corrected USART Pin configuration
      Added trace configuration to the debug description and updated Quick Start Guide
    </release>
    <release version="2.0.0" date="2015-10-09">
      Requires MDK-Middleware Version 6.5.0 and CMSIS Version 4.4.0
      Updated CMSIS-Drivers
      - CMSIS-Driver API V2.0 compliant
      - Added CMSIS-Driver for CAN
      Updated Examples:
      - Updated Board Support and all examples for MCBSTM32C and MCBSTM32E
      - Added examples for CAN
    </release>
    <release version="1.1.0" date="2015-02-13">
      Examples: Pack Selection for fixed version CMSIS 3.20.4 and MDK-Middleware 5.1.6
      Updated stm32f10x.h (xl-density: Added missing "STM32F10X_XL" for "RCC_AHBENR_DMA2EN" and "RCC_APB1ENR_USBEN")
    </release>
    <release version="1.0.5" date="2014-03-14">
      Component updated: Device:StdPeriph Drivers:Framework Version:3.5.1 - added file misc.c
    </release>
    <release version="1.0.4" date="2014-02-20">
      Added STM Peripheral Driver Library V1.3.0
    </release>
    <release version="1.0.3">
      Updated DMA driver (DMA2 peripheral added to build when HD, XL, HD_VL or CL device is used)
      USB Device driver update: multiple packet read, EP0 unconfiguration
      Updated SPI driver (IRQ handling corrected)
    </release>
    <release version="1.0.2">
      Updated drivers (namespace prefix ARM_ added)
    </release>
    <release version="1.0.1">
      Added devices
    </release>
    <release version="1.0.0">
      First Release version of STM32F1 Device Family Pack.
    </release>
  </releases>

  <keywords>
    <!-- keywords for indexing -->
    <keyword>ST</keyword>
    <keyword>Device Support</keyword>
    <keyword>Device Family Package STMicroelectronics</keyword>
    <keyword>STM32F1</keyword>
    <keyword>STM32</keyword>
  </keywords>

  <devices>
    <family Dfamily="STM32F1 Series" Dvendor="STMicroelectronics:13">
      <processor Dcore="Cortex-M3" DcoreVersion="r1p1"/>

      <book name="Documents/dui0552a_cortex_m3_dgug.pdf" title="Cortex-M3 Generic User Guide"/>

      <description>
STMicroelectronics' STM32F1 series of mainstream MCUs covers the needs of a large variety of applications in the industrial, medical and consumer markets. High performance with first-class peripherals and low-power, low-voltage operation is paired with a high level of integration at accessible prices with a simple architecture and easy-to-use tools.
Typical applications include motor drives and application control, medical and handheld equipment, industrial applications, PLCs, inverters, printers, and scanners, alarm systems, video intercom, HVAC and home audio equipment.

  - LCD parallel interface, 8080/6800 modes
  - 5 V-tolerant I/Os
  - Timer with quadrature (incremental) encoder input
  - 96-bit unique ID
      </description>

      <sequences>
        <!-- Override for Pre-Defined Sequences -->
        <sequence name="DebugDeviceUnlock">
          <block>
            Sequence("CheckID");
          </block>
        </sequence>

        <!-- Override for Pre-Defined DebugCoreStart Sequence -->
        <sequence name="DebugCoreStart">
          <block>
            // Replication of Standard Functionality
            Write32(0xE000EDF0, 0xA05F0001);                                        // Enable Core Debug via DHCSR

            // Device Specific Debug Setup
            Write32(0xE0042004, DbgMCU_CR);                                         // DBGMCU_CR: Configure MCU Debug
          </block>
        </sequence>

        <!-- Override for Pre-Defined TraceStart Sequence -->
        <sequence name="TraceStart">
          <block>
            __var traceSWO    = (__traceout &amp; 0x1) != 0;                        // SWO Trace Selected?
            __var traceTPIU   = (__traceout &amp; 0x2) != 0;                        // TPIU Trace Selected?
          </block>

          <control if="traceSWO">
            <block>
              Sequence("EnableTraceSWO");                                           // Call SWO Trace Setup
            </block>
          </control>

          <control if="traceTPIU">
            <block>
              Sequence("EnableTraceTPIU");                                          // Call TPIU Trace Setup
            </block>
          </control>
        </sequence>

        <!-- User-Defined EnableTraceSWO Sequence -->
        <sequence name="EnableTraceSWO">
          <block>
            __var dbgmcu_val    = 0;                                                // DBGMCU_CR Value

            dbgmcu_val  = Read32(0xE0042004) &amp; (~0xE0);                         // Read DBGMCU_CR and clear trace setup
            dbgmcu_val |= (1 &lt;&lt; 5);                                           // Trace I/O Enable + Trace Mode Asynchronous

            Write32(0xE0042004, dbgmcu_val);                                        // Write DBGMCU_CR: Trace Settings
          </block>
        </sequence>

        <!-- User-Defined EnableTraceTPIU Sequence -->
        <sequence name="EnableTraceTPIU">
          <block>
            __var width         = (__traceout &amp; 0x003F0000) &gt;&gt; 16;        // TPIU Port Width
            __var dbgmcu_val    = 0;                                                // DBGMCU_CR Value

            dbgmcu_val  = Read32(0xE0042004) &amp; (~0xE0);                         // Read DBGMCU_CR and clear trace setup
            dbgmcu_val |= (1 &lt;&lt; 5);                                           // Trace I/O Enable

          </block>

          <control if="width == 1">
            <block>
              // 1-Bit TPIU Trace Setup
              dbgmcu_val    |= (1 &lt;&lt; 6);                                      // Trace Mode (1-Bit)
            </block>
          </control>

          <control if="width == 2">
            <block>
              // 2-Bit TPIU Trace Setup
              dbgmcu_val    |= (2 &lt;&lt; 6);                                      // Trace Mode (2-Bit)
            </block>
          </control>

          <control if="width == 4">
            <block>
              // 4-Bit TPIU Trace Setup
              dbgmcu_val    |= (3 &lt;&lt; 6);                                      // Trace Mode (4-Bit)
            </block>
          </control>

          <block>
            // Write Trace Configuration to Target
            Write32(0xE0042004, dbgmcu_val);                                        // Write DBGMCU_CR: Trace Settings
          </block>
        </sequence>

        <!-- User-Defined Sequences -->
        <sequence name="CheckID">
          <block>
            __var pidr1 = 0;
            __var pidr2 = 0;
            __var jep106id = 0;
            __var ROMTableBase = 0;

            __ap = 0;      // AHB-AP

            ROMTableBase = ReadAP(0xF8) &amp; ~0x3;

            pidr1 = Read32(ROMTableBase + 0x0FE4);
            pidr2 = Read32(ROMTableBase + 0x0FE8);
            jep106id = ((pidr2 &amp; 0x7) &lt;&lt; 4 ) | ((pidr1 &gt;&gt; 4) &amp; 0xF);
          </block>

          <control if="jep106id != 0x20">
            <block>
              Query(0, "Not a genuine ST Device! Abort connection", 1);
              Message(2, "Not a genuine ST Device! Abort connection.");
            </block>
          </control>
        </sequence>
      </sequences>

      <!-- ************************  Subfamily 'STM32F100'  **************************** -->
      <subFamily DsubFamily="STM32F100">
        <debugvars configfile="Debug/STM32F100.dbgconf" version="1.0.0">
          __var DbgMCU_CR      = 0x00000007;   // DBGMCU_CR:  DBG_SLEEP, DBG_STOP, DBG_STANDBY
        </debugvars>

        <book name="Documents/CD00246267.pdf" title="STM32F100 Reference Manual"/>
        <feature type="WDT"           n="2"/>
        <feature type="RTC"           n="32768"/>
        <feature type="VCC"           n="2.00"    m="3.60"/>
        <feature type="Temp"          n="-40"     m="85"/>
        <feature type="Temp"          n="-40"     m="105"/>
        <feature type="DAC"           n="2"       m="12"/>

        <!-- *************************  Device 'STM32F100C4'  ***************************** -->
        <device Dname="STM32F100C4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="37"/>
          <feature type="SPI"           n="1"       m="12000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F100C6'  ***************************** -->
        <device Dname="STM32F100C6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="37"/>
          <feature type="SPI"           n="1"       m="12000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F100C8'  ***************************** -->
        <device Dname="STM32F100C8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="7"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="37"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F100CB'  ***************************** -->
        <device Dname="STM32F100CB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="7"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="37"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F100R4'  ***************************** -->
        <device Dname="STM32F100R4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="1"       m="12000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
          <feature type="BGA"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F100R6'  ***************************** -->
        <device Dname="STM32F100R6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="1"       m="12000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
          <feature type="BGA"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F100R8'  ***************************** -->
        <device Dname="STM32F100R8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="7"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="64"/>
          <feature type="BGA"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F100RB'  ***************************** -->
        <device Dname="STM32F100RB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="7"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="64"/>
          <feature type="BGA"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F100RC'  ***************************** -->
        <device Dname="STM32F100RC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F100RD'  ***************************** -->
        <device Dname="STM32F100RD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="5"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F100RE'  ***************************** -->
        <device Dname="STM32F100RE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="5"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F100V8'  ***************************** -->
        <device Dname="STM32F100V8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="7"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F100VB'  ***************************** -->
        <device Dname="STM32F100VB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00251732.pdf" title="STM32F100x4 STM32F100x6 STM32F100x8 STM32F100xB Data Sheet"/>
          <feature type="Timer"         n="7"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F100VC'  ***************************** -->
        <device Dname="STM32F100VC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="5"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F100VD'  ***************************** -->
        <device Dname="STM32F100VD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="5"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F100VE'  ***************************** -->
        <device Dname="STM32F100VE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="16"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="5"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F100ZC'  ***************************** -->
        <device Dname="STM32F100ZC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00006000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="16"/>
          <feature type="IOs"           n="112"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="5"       m="4500000"/>
          <feature type="QFP"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F100ZD'  ***************************** -->
        <device Dname="STM32F100ZD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="5"       m="4500000"/>
          <feature type="QFP"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F100ZE'  ***************************** -->
        <device Dname="STM32F100ZE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="24000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD_VL"/>
          <debug svd="SVD/STM32F100xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00212417.pdf" title="STM32F100xC STM32F100xD STM32F100xE Data Sheet"/>
          <feature type="Timer"         n="11"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="SPI"           n="3"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="5"       m="4500000"/>
          <feature type="QFP"           n="144"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32F101'  **************************** -->
      <subFamily DsubFamily="STM32F101">

        <debugvars configfile="Debug/STM32F101_102_103_105_107.dbgconf" version="1.0.0">
          __var DbgMCU_CR      = 0x00000007;   // DBGMCU_CR:  DBG_SLEEP, DBG_STOP, DBG_STANDBY
        </debugvars>

        <book name="Documents/CD00171190.pdf" title="STM32F101xx, STM32F102xx, STM32F103xx, STM32F105xx, STM32F107xx Reference Manual"/>
        <feature type="WDT"           n="2"/>
        <feature type="RTC"           n="32768"/>
        <feature type="VCC"           n="2.00"    m="3.60"/>
        <feature type="Temp"          n="-40"     m="85"/>
        <feature type="Temp"          n="-40"     m="105"/>

        <!-- *************************  Device 'STM32F101C4'  ***************************** -->
        <device Dname="STM32F101C4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210837.pdf" title="STM32F101x4 STM32F101x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F101C6'  ***************************** -->
        <device Dname="STM32F101C6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210837.pdf" title="STM32F101x4 STM32F101x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F101C8'  ***************************** -->
        <device Dname="STM32F101C8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161561.pdf" title="STM32F101x8 STM32F101xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F101CB'  ***************************** -->
        <device Dname="STM32F101CB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00004000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161561.pdf" title="STM32F101x8 STM32F101xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F101R4'  ***************************** -->
        <device Dname="STM32F101R4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210837.pdf" title="STM32F101x4 STM32F101x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101R6'  ***************************** -->
        <device Dname="STM32F101R6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210837.pdf" title="STM32F101x4 STM32F101x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101R8'  ***************************** -->
        <device Dname="STM32F101R8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161561.pdf" title="STM32F101x8 STM32F101xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101RB'  ***************************** -->
        <device Dname="STM32F101RB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00004000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161561.pdf" title="STM32F101x8 STM32F101xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101RC'  ***************************** -->
        <device Dname="STM32F101RC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101RD'  ***************************** -->
        <device Dname="STM32F101RD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101RE'  ***************************** -->
        <device Dname="STM32F101RE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101RF'  ***************************** -->
        <device Dname="STM32F101RF">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00014000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253739.pdf" title="STM32F101xF STM32F101xG Data Sheet"/>
          <feature type="Timer"         n="12"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101RG'  ***************************** -->
        <device Dname="STM32F101RG">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00014000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253739.pdf" title="STM32F101xF STM32F101xG Data Sheet"/>
          <feature type="Timer"         n="12"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F101T4'  ***************************** -->
        <device Dname="STM32F101T4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210837.pdf" title="STM32F101x4 STM32F101x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="10"      m="16"/>
          <feature type="IOs"           n="26"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="36"/>
        </device>

        <!-- *************************  Device 'STM32F101T6'  ***************************** -->
        <device Dname="STM32F101T6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210837.pdf" title="STM32F101x4 STM32F101x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="10"      m="16"/>
          <feature type="IOs"           n="26"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="36"/>
        </device>

        <!-- *************************  Device 'STM32F101T8'  ***************************** -->
        <device Dname="STM32F101T8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161561.pdf" title="STM32F101x8 STM32F101xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="26"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="36"/>
        </device>

        <!-- *************************  Device 'STM32F101TB'  ***************************** -->
        <device Dname="STM32F101TB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00004000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161561.pdf" title="STM32F101x8 STM32F101xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="26"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="36"/>
        </device>

        <!-- *************************  Device 'STM32F101V8'  ***************************** -->
        <device Dname="STM32F101V8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161561.pdf" title="STM32F101x8 STM32F101xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F101VB'  ***************************** -->
        <device Dname="STM32F101VB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00004000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161561.pdf" title="STM32F101x8 STM32F101xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F101VC'  ***************************** -->
        <device Dname="STM32F101VC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F101VD'  ***************************** -->
        <device Dname="STM32F101VD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F101VE'  ***************************** -->
        <device Dname="STM32F101VE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F101VF'  ***************************** -->
        <device Dname="STM32F101VF">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00014000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253739.pdf" title="STM32F101xF STM32F101xG Data Sheet"/>
          <feature type="Timer"         n="12"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F101VG'  ***************************** -->
        <device Dname="STM32F101VG">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00014000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253739.pdf" title="STM32F101xF STM32F101xG Data Sheet"/>
          <feature type="Timer"         n="12"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F101ZC'  ***************************** -->
        <device Dname="STM32F101ZC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00008000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F101ZD'  ***************************** -->
        <device Dname="STM32F101ZD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F101ZE'  ***************************** -->
        <device Dname="STM32F101ZE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191174.pdf" title="STM32F101xC STM32F101xD STM32F101xE Data Sheet"/>
          <feature type="Timer"         n="6"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F101ZF'  ***************************** -->
        <device Dname="STM32F101ZF">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00014000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253739.pdf" title="STM32F101xF STM32F101xG Data Sheet"/>
          <feature type="Timer"         n="12"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F101ZG'  ***************************** -->
        <device Dname="STM32F101ZG">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="36000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F101xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00014000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253739.pdf" title="STM32F101xF STM32F101xG Data Sheet"/>
          <feature type="Timer"         n="12"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="QFP"           n="144"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32F102'  **************************** -->
      <subFamily DsubFamily="STM32F102">

        <debugvars configfile="Debug/STM32F101_102_103_105_107.dbgconf" version="1.0.0">
          __var DbgMCU_CR      = 0x00000007;   // DBGMCU_CR:  DBG_SLEEP, DBG_STOP, DBG_STANDBY
        </debugvars>

        <book name="Documents/CD00171190.pdf" title="STM32F101xx, STM32F102xx, STM32F103xx, STM32F105xx, STM32F107xx Reference Manual"/>
        <feature type="WDT"           n="2"/>
        <feature type="RTC"           n="32768"/>
        <feature type="VCC"           n="2.00"    m="3.60"/>
        <feature type="Temp"          n="-40"     m="85"/>
        <feature type="Temp"          n="-40"     m="105"/>
        <feature type="USBD"          n="1"/>

        <!-- *************************  Device 'STM32F102C4'  ***************************** -->
        <device Dname="STM32F102C4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="48000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F102xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210833.pdf" title="STM32F102x4 STM32F102x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="1"       m="12000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F102C6'  ***************************** -->
        <device Dname="STM32F102C6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="48000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F102xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210833.pdf" title="STM32F102x4 STM32F102x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="10"      m="16"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="1"       m="12000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F102C8'  ***************************** -->
        <device Dname="STM32F102C8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="48000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F102xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210831.pdf" title="STM32F102x8 STM32F102xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="16"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F102CB'  ***************************** -->
        <device Dname="STM32F102CB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="48000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F102xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00004000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210831.pdf" title="STM32F102x8 STM32F102xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F102R4'  ***************************** -->
        <device Dname="STM32F102R4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="48000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F102xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210833.pdf" title="STM32F102x4 STM32F102x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="1"       m="12000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F102R6'  ***************************** -->
        <device Dname="STM32F102R6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="48000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F102xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210833.pdf" title="STM32F102x4 STM32F102x6 Data Sheet"/>
          <feature type="Timer"         n="2"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="1"       m="12000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F102R8'  ***************************** -->
        <device Dname="STM32F102R8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="48000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F102xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210831.pdf" title="STM32F102x8 STM32F102xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F102RB'  ***************************** -->
        <device Dname="STM32F102RB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="48000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F102xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00004000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210831.pdf" title="STM32F102x8 STM32F102xB Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="2"       m="12000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="64"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32F103'  **************************** -->
      <subFamily DsubFamily="STM32F103">

        <debugvars configfile="Debug/STM32F101_102_103_105_107.dbgconf" version="1.0.0">
          __var DbgMCU_CR      = 0x00000007;   // DBGMCU_CR:  DBG_SLEEP, DBG_STOP, DBG_STANDBY
        </debugvars>

        <book name="Documents/CD00171190.pdf" title="STM32F101xx, STM32F102xx, STM32F103xx, STM32F105xx, STM32F107xx Reference Manual"/>
        <feature type="WDT"           n="2"/>
        <feature type="RTC"           n="32768"/>
        <feature type="VCC"           n="2.00"    m="3.60"/>
        <feature type="Temp"          n="-40"     m="85"/>
        <feature type="Temp"          n="-40"     m="105"/>
        <feature type="USBD"          n="1"/>
        <feature type="CAN"           n="1"/>

        <!-- *************************  Device 'STM32F103C4'  ***************************** -->
        <device Dname="STM32F103C4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210843.pdf" title="STM32F103x4 STM32F103x6 Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F103C6'  ***************************** -->
        <device Dname="STM32F103C6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210843.pdf" title="STM32F103x4 STM32F103x6 Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F103C8'  ***************************** -->
        <device Dname="STM32F103C8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00005000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161566.pdf" title="STM32F103x8 STM32F103xB Data Sheet"/>
          <feature type="Timer"         n="4"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F103CB'  ***************************** -->
        <device Dname="STM32F103CB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00005000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161566.pdf" title="STM32F103x8 STM32F103xB Data Sheet"/>
          <feature type="Timer"         n="4"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="36"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="48"/>
        </device>

        <!-- *************************  Device 'STM32F103R4'  ***************************** -->
        <device Dname="STM32F103R4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210843.pdf" title="STM32F103x4 STM32F103x6 Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
          <feature type="BGA"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103R6'  ***************************** -->
        <device Dname="STM32F103R6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210843.pdf" title="STM32F103x4 STM32F103x6 Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="64"/>
          <feature type="BGA"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103R8'  ***************************** -->
        <device Dname="STM32F103R8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00005000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161566.pdf" title="STM32F103x8 STM32F103xB Data Sheet"/>
          <feature type="Timer"         n="4"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="64"/>
          <feature type="BGA"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103RB'  ***************************** -->
        <device Dname="STM32F103RB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00005000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161566.pdf" title="STM32F103x8 STM32F103xB Data Sheet"/>
          <feature type="Timer"         n="4"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="64"/>
          <feature type="BGA"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103RC'  ***************************** -->
        <device Dname="STM32F103RC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="64"/>
          <feature type="CSP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103RD'  ***************************** -->
        <device Dname="STM32F103RD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="64"/>
          <feature type="CSP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103RE'  ***************************** -->
        <device Dname="STM32F103RE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="16"      m="16"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="64"/>
          <feature type="CSP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103RF'  ***************************** -->
        <device Dname="STM32F103RF">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00018000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253742.pdf" title="STM32F103xF STM32F103xG Data Sheet"/>
          <feature type="Timer"         n="12"      m="16"/>
          <feature type="ADC"           n="16"      m="16"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103RG'  ***************************** -->
        <device Dname="STM32F103RG">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00018000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253742.pdf" title="STM32F103xF STM32F103xG Data Sheet"/>
          <feature type="Timer"         n="12"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="51"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F103T4'  ***************************** -->
        <device Dname="STM32F103T4">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00004000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00001800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_16.FLM"  start="0x08000000" size="0x00004000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210843.pdf" title="STM32F103x4 STM32F103x6 Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="26"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="36"/>
        </device>

        <!-- *************************  Device 'STM32F103T6'  ***************************** -->
        <device Dname="STM32F103T6">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_LD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00008000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00002800" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00210843.pdf" title="STM32F103x4 STM32F103x6 Data Sheet"/>
          <feature type="Timer"         n="3"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="26"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="36"/>
        </device>

        <!-- *************************  Device 'STM32F103T8'  ***************************** -->
        <device Dname="STM32F103T8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00005000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161566.pdf" title="STM32F103x8 STM32F103xB Data Sheet"/>
          <feature type="Timer"         n="4"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="26"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="36"/>
        </device>

        <!-- *************************  Device 'STM32F103TB'  ***************************** -->
        <device Dname="STM32F103TB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00005000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161566.pdf" title="STM32F103x8 STM32F103xB Data Sheet"/>
          <feature type="Timer"         n="4"       m="16"/>
          <feature type="ADC"           n="10"      m="12"/>
          <feature type="IOs"           n="26"/>
          <feature type="SPI"           n="1"       m="18000000"/>
          <feature type="I2C"           n="1"/>
          <feature type="USART"         n="2"       m="4500000"/>
          <feature type="QFP"           n="36"/>
        </device>

        <!-- *************************  Device 'STM32F103V8'  ***************************** -->
        <device Dname="STM32F103V8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00005000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161566.pdf" title="STM32F103x8 STM32F103xB Data Sheet"/>
          <feature type="Timer"         n="4"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="100"/>
          <feature type="BGA"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F103VB'  ***************************** -->
        <device Dname="STM32F103VB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_MD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00005000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_128.FLM" start="0x08000000" size="0x00020000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00161566.pdf" title="STM32F103x8 STM32F103xB Data Sheet"/>
          <feature type="Timer"         n="4"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="SPI"           n="2"       m="18000000"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="QFP"           n="100"/>
          <feature type="BGA"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F103VC'  ***************************** -->
        <device Dname="STM32F103VC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="100"/>
          <feature type="BGA"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F103VD'  ***************************** -->
        <device Dname="STM32F103VD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="100"/>
          <feature type="BGA"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F103VE'  ***************************** -->
        <device Dname="STM32F103VE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="100"/>
          <feature type="BGA"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F103VF'  ***************************** -->
        <device Dname="STM32F103VF">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00018000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253742.pdf" title="STM32F103xF STM32F103xG Data Sheet"/>
          <feature type="Timer"         n="14"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F103VG'  ***************************** -->
        <device Dname="STM32F103VG">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00018000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253742.pdf" title="STM32F103xF STM32F103xG Data Sheet"/>
          <feature type="Timer"         n="14"      m="16"/>
          <feature type="ADC"           n="16"      m="12"/>
          <feature type="IOs"           n="80"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F103ZC'  ***************************** -->
        <device Dname="STM32F103ZC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x0000C000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="21"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="144"/>
          <feature type="BGA"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F103ZD'  ***************************** -->
        <device Dname="STM32F103ZD">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00060000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="21"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="144"/>
          <feature type="BGA"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F103ZE'  ***************************** -->
        <device Dname="STM32F103ZE">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_HD"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00080000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_512.FLM" start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00191185.pdf" title="STM32F103xC STM32F103xD STM32F103xE Data Sheet"/>
          <feature type="Timer"         n="8"       m="16"/>
          <feature type="ADC"           n="21"      m="12"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="144"/>
          <feature type="BGA"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F103ZF'  ***************************** -->
        <device Dname="STM32F103ZF">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x000C0000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00018000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253742.pdf" title="STM32F103xF STM32F103xG Data Sheet"/>
          <feature type="Timer"         n="14"      m="16"/>
          <feature type="ADC"           n="21"      m="16"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="144"/>
          <feature type="BGA"           n="144"/>
        </device>

        <!-- *************************  Device 'STM32F103ZG'  ***************************** -->
        <device Dname="STM32F103ZG">
          <processor Dfpu="0" Dmpu="1" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_XL"/>
          <debug svd="SVD/STM32F103xx.svd"/>

          <memory id="IROM1"                         start="0x08000000" size="0x00100000" startup="1" default="1"/>
          <memory id="IRAM1"                         start="0x20000000" size="0x00018000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_1024.FLM" start="0x08000000" size="0x00100000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM"  start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00253742.pdf" title="STM32F103xF STM32F103xG Data Sheet"/>
          <feature type="Timer"         n="14"      m="16"/>
          <feature type="ADC"           n="21"      m="16"/>
          <feature type="IOs"           n="112"/>
          <feature type="DAC"           n="2"       m="12"/>
          <feature type="SPI"           n="3"       m="18000000"/>
          <feature type="I2S"           n="2"/>
          <feature type="I2C"           n="2"/>
          <feature type="USART"         n="3"       m="4500000"/>
          <feature type="UART"          n="2"       m="4500000"/>
          <feature type="SDIO"          n="1"/>
          <feature type="QFP"           n="144"/>
          <feature type="BGA"           n="144"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32F105'  **************************** -->
      <subFamily DsubFamily="STM32F105">

        <debugvars configfile="Debug/STM32F101_102_103_105_107.dbgconf" version="1.0.0">
          __var DbgMCU_CR      = 0x00000007;   // DBGMCU_CR:  DBG_SLEEP, DBG_STOP, DBG_STANDBY
        </debugvars>

        <book name="Documents/CD00171190.pdf" title="STM32F101xx, STM32F102xx, STM32F103xx, STM32F105xx, STM32F107xx Reference Manual"/>
        <feature type="WDT"           n="2"/>
        <feature type="RTC"           n="32768"/>
        <feature type="VCC"           n="2.00"    m="3.60"/>
        <feature type="Temp"          n="-40"     m="85"/>
        <feature type="Temp"          n="-40"     m="105"/>
        <feature type="Timer"         n="7"       m="16"/>
        <feature type="ADC"           n="16"      m="12"/>
        <feature type="DAC"           n="2"       m="12"/>
        <feature type="SPI"           n="3"       m="18000000"/>
        <feature type="I2S"           n="2"/>
        <feature type="I2C"           n="2"/>
        <feature type="USART"         n="3"       m="4500000"/>
        <feature type="UART"          n="2"       m="4500000"/>
        <feature type="CAN"           n="2"/>
        <feature type="USBOTG"        n="1"/>

        <!-- *************************  Device 'STM32F105R8'  ***************************** -->
        <device Dname="STM32F105R8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F105xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="51"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F105RB'  ***************************** -->
        <device Dname="STM32F105RB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F105xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="51"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F105RC'  ***************************** -->
        <device Dname="STM32F105RC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F105xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="51"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F105V8'  ***************************** -->
        <device Dname="STM32F105V8">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F105xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00010000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="80"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F105VB'  ***************************** -->
        <device Dname="STM32F105VB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F105xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="80"/>
          <feature type="QFP"           n="100"/>
          <feature type="BGA"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F105VC'  ***************************** -->
        <device Dname="STM32F105VC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F105xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="80"/>
          <feature type="QFP"           n="100"/>
          <feature type="BGA"           n="100"/>
        </device>
      </subFamily>

      <!-- ************************  Subfamily 'STM32F107'  **************************** -->
      <subFamily DsubFamily="STM32F107">

        <debugvars configfile="Debug/STM32F101_102_103_105_107.dbgconf" version="1.0.0">
          __var DbgMCU_CR      = 0x00000007;   // DBGMCU_CR:  DBG_SLEEP, DBG_STOP, DBG_STANDBY
        </debugvars>

        <book name="Documents/CD00171190.pdf" title="STM32F101xx, STM32F102xx, STM32F103xx, STM32F105xx, STM32F107xx Reference Manual"/>
        <feature type="WDT"           n="2"/>
        <feature type="RTC"           n="32768"/>
        <feature type="VCC"           n="2.00"    m="3.60"/>
        <feature type="Temp"          n="-40"     m="85"/>
        <feature type="Temp"          n="-40"     m="105"/>
        <feature type="Timer"         n="7"       m="16"/>
        <feature type="ADC"           n="16"      m="12"/>
        <feature type="DAC"           n="2"       m="12"/>
        <feature type="SPI"           n="3"       m="18000000"/>
        <feature type="I2S"           n="2"/>
        <feature type="I2C"           n="2"/>
        <feature type="USART"         n="3"       m="4500000"/>
        <feature type="UART"          n="2"       m="4500000"/>
        <feature type="CAN"           n="2"/>
        <feature type="USBOTG"        n="1"/>
        <feature type="ETH"           n="1"/>

        <!-- *************************  Device 'STM32F107RB'  ***************************** -->
        <device Dname="STM32F107RB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F107xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="51"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F107RC'  ***************************** -->
        <device Dname="STM32F107RC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F107xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="51"/>
          <feature type="QFP"           n="64"/>
        </device>

        <!-- *************************  Device 'STM32F107VB'  ***************************** -->
        <device Dname="STM32F107VB">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F107xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00020000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="80"/>
          <feature type="QFP"           n="100"/>
        </device>

        <!-- *************************  Device 'STM32F107VC'  ***************************** -->
        <device Dname="STM32F107VC">
          <processor Dfpu="0" Dmpu="0" Dendian="Little-endian" Dclock="72000000"/>
          <compile header="Device/Include/stm32f10x.h"  define="STM32F10X_CL"/>
          <debug svd="SVD/STM32F107xx.svd"/>

          <memory id="IROM1"                        start="0x08000000" size="0x00040000" startup="1" default="1"/>
          <memory id="IRAM1"                        start="0x20000000" size="0x00010000" init   ="0" default="1"/>
          <algorithm name="Flash/STM32F10x_CL.FLM"  start="0x08000000" size="0x00080000"             default="1"/>
          <algorithm name="Flash/STM32F10x_OPT.FLM" start="0x1FFFF800" size="0x00000010"             default="0"/>

          <book name="Documents/CD00220364.pdf" title="STM32F105xx STM32F107xx Data Sheet"/>
          <feature type="IOs"           n="80"/>
          <feature type="QFP"           n="100"/>
        </device>
      </subFamily>

    </family>
  </devices>

  <conditions>
    <!-- conditions selecting Compiler -->
    <condition id="Compiler ARM">
      <require Tcompiler="ARMCC"/>
    </condition>

    <condition id="STM32F1xx">
      <description>STMicroelectronics STM32F1xx Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F1*"/>
    </condition>
    <condition id="STM32F1xx VL LD">
      <description>STMicroelectronics STM32F1xx Value Line Low Density Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F100?[46]"/>
    </condition>
    <condition id="STM32F1xx VL MD">
      <description>STMicroelectronics STM32F1xx Value Line Mid Density Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F100?[8B]"/>
    </condition>
    <condition id="STM32F1xx VL HD">
      <description>STMicroelectronics STM32F1xx Value Line Mid Density Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F100?[CDE]"/>
    </condition>
    <condition id="STM32F1xx LD">
      <description>STMicroelectronics STM32F1xx Low Density Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F10[123]?[46]"/>
    </condition>
    <condition id="STM32F1xx MD">
      <description>STMicroelectronics STM32F1xx Mid Density Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F10[123]?[8B]"/>
    </condition>
    <condition id="STM32F1xx HD">
      <description>STMicroelectronics STM32F1xx High Density Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F10[123]?[CDE]"/>
    </condition>
    <condition id="STM32F1xx XL">
      <description>STMicroelectronics STM32F1xx XL Density Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F10[123]?[FG]"/>
    </condition>
    <condition id="STM32F1xx CL">
      <description>STMicroelectronics STM32F1xx Connectivity Line Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F10[57]??"/>
    </condition>

    <condition id="STM32F1xx VL LD ARMCC">
      <description>filter for STM32F1xx Value Line Low Density Device and the ARM compiler</description>
      <require condition="STM32F1xx VL LD"/>
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="STM32F1xx VL MD ARMCC">
      <description>filter for STM32F1xx Value Line Mid Density Device and the ARM compiler</description>
      <require condition="STM32F1xx VL MD"/>
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="STM32F1xx VL HD ARMCC">
      <description>filter for STM32F1xx Value Line Mid Density Device and the ARM compiler</description>
      <require condition="STM32F1xx VL HD"/>
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="STM32F1xx LD ARMCC">
      <description>filter for STM32F1xx Low Density Device and the ARM compiler</description>
      <require condition="STM32F1xx LD"/>
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="STM32F1xx MD ARMCC">
      <description>filter for STM32F1xx Mid Density Device and the ARM compiler</description>
      <require condition="STM32F1xx MD"/>
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="STM32F1xx HD ARMCC">
      <description>filter for STM32F1xx High Density Device and the ARM compiler</description>
      <require condition="STM32F1xx HD"/>
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="STM32F1xx XL ARMCC">
      <description>filter for STM32F1xx XL Density Device and the ARM compiler</description>
      <require condition="STM32F1xx XL"/>
      <require Tcompiler="ARMCC"/>
    </condition>
    <condition id="STM32F1xx CL ARMCC">
      <description>filter for STM32F1xx Connectivity Line Device and the ARM compiler</description>
      <require condition="STM32F1xx CL"/>
      <require Tcompiler="ARMCC"/>
    </condition>

    <condition id="STM32F107">
      <description>STMicroelectronics STM32F107 Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F107??"/>
    </condition>
    <condition id="STM32F102_3">
      <description>STMicroelectronics STM32F102/103 Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F10[23]??"/>
    </condition>
    <condition id="STM32F105_7">
      <description>STMicroelectronics STM32F105/107 Device</description>
      <require Dvendor="STMicroelectronics:13" Dname="STM32F10[57]??"/>
    </condition>

    <condition id="STM32F1xx CMSIS">
      <description>STMicroelectronics STM32F1xx Device with CMSIS</description>
      <require condition="STM32F1xx"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
    </condition>

    <condition id="STM32F1xx CMSIS GPIO">
      <description>STMicroelectronics STM32F1xx Device with CMSIS and GPIO</description>
      <require condition="STM32F1xx"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="Device" Cgroup="GPIO" />
    </condition>
    <condition id="STM32F107 CMSIS GPIO">
      <description>STMicroelectronics STM32F107 Device with CMSIS and GPIO</description>
      <require condition="STM32F107"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="Device" Cgroup="GPIO" />
    </condition>

    <condition id="STM32F1xx CMSIS GPIO DMA">
      <description>STMicroelectronics STM32F1xx Device with CMSIS, GPIO and DMA</description>
      <require condition="STM32F1xx"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="Device" Cgroup="GPIO" />
      <require Cclass="Device" Cgroup="DMA" />
    </condition>

    <condition id="STM32F1xx CMSIS GPIO SPI">
      <description>STMicroelectronics STM32F1xx Device with CMSIS, GPIO and SPI Driver</description>
      <require condition="STM32F1xx"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="CMSIS Driver" Cgroup="SPI" />
    </condition>

    <condition id="STM32F102_3 CMSIS RTOS GPIO">
      <description>STMicroelectronics STM32F102/103 Device with CMSIS, RTOS and GPIO</description>
      <require condition="STM32F102_3"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <accept Cclass="CMSIS" Cgroup="RTOS"/>
      <accept Cclass="CMSIS" Cgroup="RTOS2"/>
      <require Cclass="Device" Cgroup="GPIO" />
    </condition>
    <condition id="STM32F105_7 CMSIS RTOS GPIO">
      <description>STMicroelectronics STM32F105/107 Device with CMSIS, RTOS and GPIO</description>
      <require condition="STM32F105_7"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <accept Cclass="CMSIS" Cgroup="RTOS"/>
      <accept Cclass="CMSIS" Cgroup="RTOS2"/>
      <require Cclass="Device" Cgroup="GPIO" />
    </condition>

    <condition id="STM32F1xx CMSIS RTOS I2C">
      <description>STMicroelectronics STM32F1xx Device with CMSIS, RTOS and I2C Driver</description>
      <require condition="STM32F1xx"/>
      <require Cclass="CMSIS" Cgroup="CORE"/>
      <require Cclass="CMSIS" Cgroup="RTOS"/>
      <require Cclass="CMSIS Driver" Cgroup="I2C" />
    </condition>

    <condition id="STM32F1xx STDPERIPH">
      <description>STMicroelectronics STM32F1xx with Standard Peripherals Drivers Framework</description>
      <require condition="STM32F1xx CMSIS"/>
      <require Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework"/>
    </condition>
    <condition id="STM32F1xx STDPERIPH RCC">
      <description>STMicroelectronics STM32F1xx with Standard Peripherals Drivers Framework and RCC</description>
      <require condition="STM32F1xx CMSIS"/>
      <require Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework"/>
      <require Cclass="Device" Cgroup="StdPeriph Drivers" Csub="RCC"/>
    </condition>
  </conditions>

  <components>
    <component Cclass="Device" Cgroup="Startup" Cversion="1.0.0" condition="STM32F1xx CMSIS">
      <description>System Startup for STMicroelectronics STM32F1xx device series</description>
      <files>
        <!--  include folder -->
        <file category="include" name="Device/Include/"/>

        <!-- startup files -->
        <file category="source" name="Device/Source/ARM/startup_stm32f10x_ld_vl.s" attr="config" version="1.0.0" condition="STM32F1xx VL LD ARMCC"/>
        <file category="source" name="Device/Source/ARM/startup_stm32f10x_md_vl.s" attr="config" version="1.0.0" condition="STM32F1xx VL MD ARMCC"/>
        <file category="source" name="Device/Source/ARM/startup_stm32f10x_hd_vl.s" attr="config" version="1.0.0" condition="STM32F1xx VL HD ARMCC"/>
        <file category="source" name="Device/Source/ARM/startup_stm32f10x_ld.s"    attr="config" version="1.0.0" condition="STM32F1xx LD ARMCC"/>
        <file category="source" name="Device/Source/ARM/startup_stm32f10x_md.s"    attr="config" version="1.0.0" condition="STM32F1xx MD ARMCC"/>
        <file category="source" name="Device/Source/ARM/startup_stm32f10x_hd.s"    attr="config" version="1.0.0" condition="STM32F1xx HD ARMCC"/>
        <file category="source" name="Device/Source/ARM/startup_stm32f10x_xl.s"    attr="config" version="1.0.0" condition="STM32F1xx XL ARMCC"/>
        <file category="source" name="Device/Source/ARM/startup_stm32f10x_cl.s"    attr="config" version="1.0.0" condition="STM32F1xx CL ARMCC"/>

        <!-- system file -->
        <file category="source" name="Device/Source/system_stm32f10x.c"            attr="config" version="1.0.0" />
        <!-- device configuration required by drivers at the moment -->
        <file category="header" name="RTE_Driver/Config/RTE_Device.h"              attr="config" version="1.1.2"/>

        <!-- Flash Option Bytes templates -->
        <file category="source" name="Device/Source/ARM/STM32F1xx_OPT.s"           attr="template" select="Flash Option Bytes Template" condition="Compiler ARM"/>
      </files>
    </component>

    <!-- START: STMicroelectronics Standard Peripherals Drivers -->
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Framework" Cversion="3.5.1" condition="STM32F1xx STDPERIPH">
      <description>Standard Peripherals Drivers Framework</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_FRAMEWORK
      </RTE_Components_h>
      <files>
        <file category="doc"     name="Device/StdPeriph_Driver/stm32f10x_stdperiph_lib_um.chm"/>
        <file category="include" name="Device/StdPeriph_Driver/inc/"/>
        <file category="header"  name="Device/StdPeriph_Driver/inc/misc.h"/>
        <file category="source"  name="Device/StdPeriph_Driver/src/misc.c"/>
        <file category="source"  name="Device/StdPeriph_Driver/templates/stm32f10x_conf.h" attr="config" version="3.5.0"/>
        <file category="header"  name="Device/StdPeriph_Driver/templates/stm32f10x_it.h"   attr="template" select="Interrupt Service Routines"/>
        <file category="source"  name="Device/StdPeriph_Driver/templates/stm32f10x_it.c"   attr="template" select="Interrupt Service Routines"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="ADC"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Analog-to-digital converter (ADC) driver for STM32F10x</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_ADC
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_adc.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_adc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="BKP"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH">
      <description>Backup registers (BKP) driver for STM32F10x</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_BKP
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_bkp.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_bkp.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CAN"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Controller area network (CAN) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_CAN
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_can.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_can.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CEC"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Consumer electronics control controller (CEC) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_CEC
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_cec.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_cec.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="CRC"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH ">
      <description>CRC calculation unit (CRC) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_CRC
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_crc.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_crc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="DAC"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Digital-to-analog converter (DAC) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_DAC
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_dac.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_dac.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="DBGMCU"    Cversion="3.5.0" condition="STM32F1xx STDPERIPH">
      <description>MCU debug component (DBGMCU) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_DBGMCU
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_dbgmcu.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_dbgmcu.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="DMA"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>DMA controller (DMA) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_DMA
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_dma.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_dma.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="EXTI"      Cversion="3.5.0" condition="STM32F1xx STDPERIPH">
      <description>External interrupt/event controller (EXTI) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_EXTI
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_exti.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_exti.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="Flash"     Cversion="3.5.0" condition="STM32F1xx STDPERIPH">
      <description>Embedded Flash memory driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_FLASH
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_flash.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_flash.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="FSMC"      Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Flexible Static Memory Controller (FSMC) driver for STM32F11x</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_FSMC
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_fsmc.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_fsmc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="GPIO"      Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>General-purpose I/O (GPIO) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_GPIO
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_gpio.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_gpio.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="I2C"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Inter-integrated circuit (I2C) interface driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_I2C
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_i2c.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_i2c.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="IWDG"      Cversion="3.5.0" condition="STM32F1xx STDPERIPH ">
      <description>Independent watchdog (IWDG) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_IWDG
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_iwdg.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_iwdg.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="PWR"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Power controller (PWR) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_PWR
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_pwr.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_pwr.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="RCC"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH">
      <description>Reset and clock control (RCC) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_RCC
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_rcc.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_rcc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="RTC"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH">
      <description>Real-time clock (RTC) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_RTC
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_rtc.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_rtc.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SDIO"      Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Secure digital (SDIO) interface driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_SDIO
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_sdio.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_sdio.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="SPI"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Serial peripheral interface (SPI) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_SPI
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_spi.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_spi.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="TIM"       Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Timers (TIM) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_TIM
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_tim.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_tim.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="USART"     Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Universal synchronous asynchronous receiver transmitter (USART) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_USART
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_usart.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_usart.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="StdPeriph Drivers" Csub="WWDG"      Cversion="3.5.0" condition="STM32F1xx STDPERIPH RCC">
      <description>Window watchdog (WWDG) driver for STM32F1xx</description>
      <RTE_Components_h>
        #define RTE_DEVICE_STDPERIPH_WWDG
      </RTE_Components_h>
      <files>
        <file category="header" name="Device/StdPeriph_Driver/inc/stm32f10x_wwdg.h"/>
        <file category="source" name="Device/StdPeriph_Driver/src/stm32f10x_wwdg.c"/>
      </files>
    </component>
    <!-- END: STMicroelectronics Standard Peripherals Drivers -->

    <component Cclass="Device" Cgroup="GPIO" Cversion="1.3" condition="STM32F1xx CMSIS">
      <description>GPIO driver used by RTE Drivers for STM32F1 Series</description>
      <files>
        <file category="header" name="RTE_Driver/GPIO_STM32F10x.h"/>
        <file category="source" name="RTE_Driver/GPIO_STM32F10x.c"/>
      </files>
    </component>
    <component Cclass="Device" Cgroup="DMA"  Cversion="1.2" condition="STM32F1xx CMSIS">
      <description>DMA driver used by RTE Drivers for STM32F1 Series</description>
      <files>
        <file category="header" name="RTE_Driver/DMA_STM32F10x.h"/>
        <file category="source" name="RTE_Driver/DMA_STM32F10x.c"/>
      </files>
    </component>

    <component Cclass="CMSIS Driver" Cgroup="CAN"          Capiversion="1.0" Cversion="1.9" condition="STM32F1xx CMSIS GPIO">
      <description>CAN Driver for STM32F1 Series</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_CAN1                /* Driver CAN1 */
        #define RTE_Drivers_CAN2                /* Driver CAN2 */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/CAN_STM32F10x.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="SPI"          Capiversion="2.1" Cversion="2.2" condition="STM32F1xx CMSIS GPIO DMA">
      <description>SPI Driver for STM32F1 Series</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_SPI1                /* Driver SPI1 */
        #define RTE_Drivers_SPI2                /* Driver SPI2 */
        #define RTE_Drivers_SPI3                /* Driver SPI3 */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/SPI_STM32F10x.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USART"        Capiversion="2.1" Cversion="2.3" condition="STM32F1xx CMSIS GPIO DMA">
      <description>UART Driver for STM32F1 Series</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_USART1              /* Driver USART1 */
        #define RTE_Drivers_USART2              /* Driver USART2 */
        #define RTE_Drivers_USART3              /* Driver USART3 */
        #define RTE_Drivers_USART4              /* Driver UART4  */
        #define RTE_Drivers_USART5              /* Driver UART5  */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/USART_STM32F10x.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="I2C"          Capiversion="2.2" Cversion="2.1" condition="STM32F1xx CMSIS GPIO DMA">
      <description>I2C Driver for STM32F1 Series</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_I2C1                /* Driver I2C1 */
        #define RTE_Drivers_I2C2                /* Driver I2C2 */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/I2C_STM32F10x.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="MCI"          Capiversion="2.2" Cversion="2.0" condition="STM32F1xx CMSIS GPIO DMA">
      <description>MCI Driver for STM32F1 Series</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_MCI0                /* Driver MCI0 */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/MCI_STM32F10x.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="Ethernet MAC" Capiversion="2.1" Cversion="2.2" condition="STM32F107 CMSIS GPIO">
      <description>Ethernet MAC Driver for STM32F1 Series</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_ETH_MAC0            /* Driver ETH_MAC0 */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/EMAC_STM32F10x.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USB Device"   Capiversion="2.1" Cversion="2.1" condition="STM32F102_3 CMSIS RTOS GPIO">
      <description>USB Device Driver for STM32F1 Series</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_USBD0               /* Driver USBD0 */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/USBD_STM32F10x.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USB Device"   Capiversion="2.1" Cversion="2.4" condition="STM32F105_7 CMSIS RTOS GPIO">
      <description>USB Device Driver for STM32F1 Connectivity Line Devices</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_USBD0               /* Driver USBD0 */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/OTG_STM32F10x_cl.c"/>
        <file category="source" name="RTE_Driver/USBD_STM32F10x_cl.c"/>
      </files>
    </component>
    <component Cclass="CMSIS Driver" Cgroup="USB Host"     Capiversion="2.1" Cversion="2.4" condition="STM32F105_7 CMSIS RTOS GPIO">
      <description>USB Host Driver for STM32F1 Connectivity Line Devices</description>
      <RTE_Components_h>  <!-- the following content goes into file 'RTE_Components.h' -->
        #define RTE_Drivers_USBH0               /* Driver USBH0 */
      </RTE_Components_h>
      <files>
        <file category="source" name="RTE_Driver/OTG_STM32F10x_cl.c"/>
        <file category="source" name="RTE_Driver/USBH_STM32F10x_cl.c"/>
      </files>
    </component>

    <!-- MCBSTM32E Development Board -->
    <bundle Cbundle="MCBSTM32E" Cclass="Board Support" Cversion="2.0.0">
      <description>Keil Development Board MCBSTM32E</description>
      <doc>Boards/Keil/MCBSTM32E/Documentation/mcbstm32e.chm</doc>
      <component Cgroup="A/D Converter" Capiversion="1.00" condition="STM32F1xx CMSIS">
        <description>A/D Converter Interface for Keil MCBSTM32E Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32E/Common/ADC_MCBSTM32E.c"/>
        </files>
      </component>
      <component Cgroup="Graphic LCD" Capiversion="1.0.0" condition="STM32F1xx CMSIS">
        <description>Graphic LCD Interface for Keil MCBSTM32E Development Board</description>
        <files>
          <file category="header" name="Boards/Keil/MCBSTM32E/Common/GLCD_Config.h"/>
          <file category="source" name="Boards/Keil/MCBSTM32E/Common/GLCD_Fonts.c"/>
          <file category="source" name="Boards/Keil/MCBSTM32E/Common/GLCD_MCBSTM32E.c"/>
        </files>
      </component>
      <component Cgroup="emWin LCD" Capiversion="1.1.0" Cvariant="16-bit IF" condition="STM32F1xx CMSIS">
        <description>emWin LCD 16-bit Interface for Keil MCBSTM32E Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32E/Common/LCD_X.c"/>
        </files>
      </component>
      <component Cgroup="Joystick" Capiversion="1.0.0" condition="STM32F1xx CMSIS GPIO">
        <description>Joystick Interface for Keil MCBSTM32E Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32E/Common/Joystick_MCBSTM32E.c"/>
        </files>
      </component>
      <component Cgroup="Buttons" Capiversion="1.0.0" condition="STM32F1xx CMSIS GPIO">
        <description>Buttons Interface for Keil MCBSTM32E Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32E/Common/Buttons_MCBSTM32E.c"/>
        </files>
      </component>
      <component Cgroup="LED" Capiversion="1.0.0" condition="STM32F1xx CMSIS GPIO">
        <description>LED Interface for Keil MCBSTM32E Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32E/Common/LED_MCBSTM32E.c"/>
        </files>
      </component>
    </bundle>

    <!-- MCBSTM32C Development Board -->
    <bundle Cbundle="MCBSTM32C" Cclass="Board Support" Cversion="2.0.0">
      <description>Keil Development Board MCBSTM32C</description>
      <doc>Boards/Keil/MCBSTM32C/Documentation/mcbstm32c.chm</doc>
      <component Cgroup="A/D Converter" Capiversion="1.0.0" condition="STM32F1xx CMSIS">
        <description>A/D Converter Interface for Keil MCBSTM32C Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32C/Common/ADC_MCBSTM32C.c"/>
        </files>
      </component>
      <component Cgroup="Graphic LCD" Capiversion="1.0.0" condition="STM32F1xx CMSIS GPIO SPI">
        <description>Graphic LCD Interface for Keil MCBSTM32C Development Board</description>
        <files>
          <file category="header" name="Boards/Keil/MCBSTM32C/Common/GLCD_Config.h"/>
          <file category="source" name="Boards/Keil/MCBSTM32C/Common/GLCD_Fonts.c"/>
          <file category="source" name="Boards/Keil/MCBSTM32C/Common/GLCD_MCBSTM32C.c"/>
        </files>
      </component>
      <component Cgroup="emWin LCD" Capiversion="1.1.0" Cvariant="SPI IF" condition="STM32F1xx CMSIS GPIO SPI">
        <description>emWin LCD SPI Interface for Keil MCBSTM32C Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32C/Common/LCD_X.c"/>
        </files>
      </component>
      <component Cgroup="Joystick" Capiversion="1.0.0" condition="STM32F1xx CMSIS GPIO">
        <description>Joystick Interface for Keil MCBSTM32C Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32C/Common/Joystick_MCBSTM32C.c"/>
        </files>
      </component>
      <component Cgroup="Buttons" Capiversion="1.0.0" condition="STM32F1xx CMSIS GPIO">
        <description>Buttons Interface for Keil MCBSTM32C Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32C/Common/Buttons_MCBSTM32C.c"/>
        </files>
      </component>
      <component Cgroup="LED" Capiversion="1.0.0" condition="STM32F1xx CMSIS GPIO">
        <description>LED Interface for Keil MCBSTM32C Development Board</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32C/Common/LED_MCBSTM32C.c"/>
        </files>
      </component>
      <component Cgroup="Touchscreen" Capiversion="1.0.0" condition="STM32F1xx CMSIS RTOS I2C">
        <description>Touchscreen Interface for STMPE811</description>
        <files>
          <file category="source" name="Boards/Keil/MCBSTM32C/Common/Touch_STMPE811.c"/>
        </files>
      </component>
    </bundle>
  </components>

  <boards>
    <board vendor="Keil" name="MCBSTM32C" revision="Ver 2.0" salesContact="<EMAIL>" orderForm="http://www.keil.com/product/prices.asp?MCBSTM32C=ON">
      <description>Keil MCBSTM32C Development Board</description>
      <image small="Boards/Keil/MCBSTM32C/Documentation/mcbstm32c_small.jpg"
             large="Boards/Keil/MCBSTM32C/Documentation/mcbstm32c_large.jpg"/>
      <book category="overview"  name="http://www.keil.com/mcbstm32c/" title="MCBSTM32C Evaluation Board Web Page"/>
      <book category="setup"     name="Boards/Keil/MCBSTM32C/Documentation/STM32C_QSG.pdf" title="Quick Start Guide"/>
      <book category="schematic" name="Boards/Keil/MCBSTM32C/Documentation/mcbstm32c-base-board-schematics.pdf" title="Base Board Schematics"/>
      <book category="schematic" name="Boards/Keil/MCBSTM32C/Documentation/mcbstm32c-display-board-schematics.pdf" title="Display Board Schematics"/>
      <book category="manual"    name="Boards/Keil/MCBSTM32C/Documentation/mcbstm32c.chm" title="User Manual"/>
      <mountedDevice    deviceIndex="0" Dvendor="STMicroelectronics:13" Dname="STM32F107VC"/>
      <compatibleDevice deviceIndex="0" Dvendor="STMicroelectronics:13" DsubFamily="STM32F105"/>
      <compatibleDevice deviceIndex="0" Dvendor="STMicroelectronics:13" DsubFamily="STM32F107"/>
      <feature type="XTAL"            n="8000000"/>
      <feature type="PWR"             n="5"              name="USB Powered"/>
      <feature type="ROM"             n="1"              name="8 kByte I2C EEPROM"/>
      <feature type="Proto"           n="6"              name="Prototyping areas connected to I/Os"/>
      <feature type="DIO"             n="80"/>
      <feature type="USB"             n="1"              name="USB 2.0 Full Speed, Host/Device, OTG"/>
      <feature type="CAN"             n="2"/>
      <feature type="RS232"           n="1"/>
      <feature type="ETH"             n="1"              name="10/100 Ethernet Port"/>
      <feature type="GLCD"            n="1"  m="240.320" name="2.4 inch Color QVGA TFT LCD with resistive touchscreen"/>
      <feature type="Gyro"            n="1"              name="3-axis digital output gyroscope"/>
      <feature type="Joystick"        n="1"              name="5-position Joystick"/>
      <feature type="Accelerometer"   n="1"              name="3-axis digital Accelerometer"/>
      <feature type="Poti"            n="1"              name="Analog Voltage Control for ADC Input (potentiometer)"/>
      <feature type="LineIn"          n="2"              name="Audio CODEC with Line-In/Out and Speaker/Microphone"/>
      <feature type="LineOut"         n="2"              name="Audio CODEC with Line-In/Out and Speaker/Microphone"/>
      <feature type="Button"          n="4"              name="Push-Buttons for Reset, Wakeup, Tamper and User"/>
      <feature type="LED"             n="8"              name="LEDs directly connected to port pins"/>
      <feature type="CustomFF"        n="115" m="115"/>
      <debugInterface adapter="JTAG/SW" connector="20 pin JTAG (0.1 inch connector)"/>
      <debugInterface adapter="JTAG/SW" connector="10 pin Cortex debug (0.05 inch connector)"/>
      <debugInterface adapter="JTAG/SW" connector="20-pin Cortex debug + ETM Trace (0.05 inch connector)"/>
    </board>

    <board vendor="Keil" name="MCBSTM32E" revision="Ver 3.0" salesContact="<EMAIL>" orderForm="http://www.keil.com/product/prices.asp?MCBSTM32EXL=ON">
      <description>Keil MCBSTM32E Development Board</description>
      <image small="Boards/Keil/MCBSTM32E/Documentation/mcbstm32e_small.jpg"
             large="Boards/Keil/MCBSTM32E/Documentation/mcbstm32e_large.jpg"/>
      <book category="overview"  name="http://www.keil.com/mcbstm32e/" title="MCBSTM32E Evaluation Board Web Page"/>
      <book category="setup"     name="Boards/Keil/MCBSTM32E/Documentation/STM32E_QSG.pdf" title="Quick Start Guide"/>
      <book category="schematic" name="Boards/Keil/MCBSTM32E/Documentation/mcbstm32e-base-board-schematics.pdf" title="Base Board Schematics"/>
      <book category="schematic" name="Boards/Keil/MCBSTM32E/Documentation/mcbstm32e-display-board-schematics.pdf" title="Display Board Schematics"/>
      <book category="manual"    name="Boards/Keil/MCBSTM32E/Documentation/mcbstm32e.chm" title="User Manual"/>
      <mountedDevice    deviceIndex="0" Dvendor="STMicroelectronics:13" Dname="STM32F103ZG"/>
      <compatibleDevice deviceIndex="0" Dvendor="STMicroelectronics:13" DsubFamily="STM32F103"/>
      <feature type="XTAL"            n="8000000"/>
      <feature type="PWR"             n="5"              name="USB Powered"/>
      <feature type="RAM"             n="1"              name="1 MByte SRAM"/>
      <feature type="ROM"             n="1"              name="8 MByte SPI Flash"/>
      <feature type="Proto"           n="6"              name="Prototyping areas connected to I/Os"/>
      <feature type="DIO"             n="42"             name="GPIOs (maximum)"/>
      <feature type="USB"             n="1"              name="USB 2.0 Full Speed Device"/>
      <feature type="CAN"             n="1"/>
      <feature type="RS232"           n="1"/>
      <feature type="GLCD"            n="1"  m="240.320" name="2.4 inch Color QVGA TFT LCD with resistive touchscreen"/>
      <feature type="Joystick"        n="1"              name="5-position Joystick"/>
      <feature type="Poti"            n="1"              name="Analog Voltage Control for ADC Input (potentiometer)"/>
      <feature type="LineIn"          n="2"              name="Audio CODEC with Line-In/Out and Speaker/Microphone"/>
      <feature type="LineOut"         n="2"              name="Audio CODEC with Line-In/Out and Speaker/Microphone"/>
      <feature type="Button"          n="4"              name="Push-Buttons for Reset, Wakeup, Tamper and User"/>
      <feature type="LED"             n="8"              name="LEDs directly connected to port pins"/>
      <feature type="CustomFF"        n="110" m="140"/>
      <debugInterface adapter="JTAG/SW" connector="20 pin JTAG (0.1 inch connector)"/>
      <debugInterface adapter="JTAG/SW" connector="20-pin Cortex debug + ETM Trace (0.05 inch connector)"/>
    </board>
  </boards>

  <examples>
    <!-- MCBSTM32E Development Board -->
    <example name="CMSIS-RTOS Blinky" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/Blinky">
      <description>CMSIS-RTOS Blinky example</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="Blinky.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="CMSIS" Cgroup="CORE"/>
        <component Cclass="Device" Cgroup="Startup"/>
        <component Cclass="CMSIS" Cgroup="RTOS"/>
        <category>Getting Started</category>
        <category>CMSIS-RTX</category>
      </attributes>
    </example>

    <example name="emWin Example" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/emWin/Example">
      <description>emWin Graphics simple example</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="Example.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Graphics" Cgroup="CORE"/>
        <category>Middleware</category>
        <category>Graphics</category>
        <keyword>emWin</keyword>
      </attributes>
    </example>

    <example name="emWin GUI Demo" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/emWin/GUIDemo">
      <description>emWin Graphics Demo example</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="GUIDemo.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Graphics" Cgroup="CORE"/>
        <category>Middleware</category>
        <category>Graphics</category>
        <keyword>emWin</keyword>
      </attributes>
    </example>

    <example name="CAN" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/Middleware/CAN/CAN">
      <description>CAN example that sends and receives data messages</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="CAN.uvprojx"/>
      </project>
      <attributes>
        <category>Middleware</category>
        <category>CAN</category>
      </attributes>
    </example>

    <example name="CAN RTR" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/Middleware/CAN/CAN_RTR">
      <description>CAN example that demonstrates Remote Transmission Request (RTR) usage</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="CAN_RTR.uvprojx"/>
      </project>
      <attributes>
        <category>Middleware</category>
        <category>CAN</category>
      </attributes>
    </example>

    <example name="USB Device HID" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/Middleware/USB/Device/HID" version="1.0.0">
      <description>USB Human Interface Device providing access from PC to board LEDs and Joystick</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="HID.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="USB" Cgroup="Device" Csub="HID"/>
        <category>Middleware</category>
        <category>USB Device</category>
        <keyword>HID</keyword>
      </attributes>
    </example>

    <example name="USB Device Mass Storage" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/Middleware/USB/Device/MassStorage" version="1.0.0">
      <description>USB Mass Storage Device using SD/MMC Memory Card or RAM as storage media</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="MassStorage.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="USB" Cgroup="Device" Csub="MSC"/>
        <category>Middleware</category>
        <category>USB Device</category>
        <keyword>Memory Disk</keyword>
      </attributes>
    </example>

    <example name="USB Device Virtual COM" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/Middleware/USB/Device/VirtualCOM" version="1.0.0">
      <description>Bridge between PC USB Virtual COM Port and UART port</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="VirtualCOM.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="USB" Cgroup="Device" Csub="CDC"/>
        <category>Middleware</category>
        <category>USB Device</category>
        <keyword>Virtual COM</keyword>
      </attributes>
    </example>

    <example name="File System Demo" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32E/Middleware/FileSystem/File_Demo" version="1.0.0">
      <description>File manipulation example: create, read, copy, delete files on any enabled drive (SD/MMC Card, NOR/NAND Flash, RAM) and format each drive</description>
      <board name="MCBSTM32E" vendor="Keil"/>
      <project>
        <environment name="uv" load="File_Demo.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="File System" Cgroup="Drive"/>
        <category>Middleware</category>
        <category>File System</category>
        <keyword>SD/MMC Card</keyword>
      </attributes>
    </example>

    <!-- MCBSTM32C Development Board -->
    <example name="CMSIS-RTOS Blinky" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Blinky">
      <description>CMSIS-RTOS Blinky example</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="Blinky.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="CMSIS" Cgroup="CORE"/>
        <component Cclass="Device" Cgroup="Startup"/>
        <component Cclass="CMSIS" Cgroup="RTOS"/>
        <category>Getting Started</category>
        <category>CMSIS-RTX</category>
      </attributes>
    </example>

    <example name="Demo" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Demo">
      <description>Demo example</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="Demo.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="CMSIS" Cgroup="CORE"/>
        <component Cclass="Device" Cgroup="Startup"/>
        <category>Getting Started</category>
      </attributes>
    </example>

    <example name="emWin Example" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/emWin/Example">
      <description>emWin Graphics simple example</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="Example.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Graphics" Cgroup="CORE"/>
        <category>Middleware</category>
        <category>Graphics</category>
        <keyword>emWin</keyword>
      </attributes>
    </example>

    <example name="emWin GUI Demo" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/emWin/GUIDemo">
      <description>emWin Graphics Demo example</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="GUIDemo.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Graphics" Cgroup="CORE"/>
        <category>Middleware</category>
        <category>Graphics</category>
        <keyword>emWin</keyword>
      </attributes>
    </example>

    <example name="CAN" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/CAN/CAN">
      <description>CAN example that sends and receives data messages</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="CAN.uvprojx"/>
      </project>
      <attributes>
        <category>Middleware</category>
        <category>CAN</category>
      </attributes>
    </example>

    <example name="CAN RTR" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/CAN/CAN_RTR">
      <description>CAN example that demonstrates Remote Transmission Request (RTR) usage</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="CAN_RTR.uvprojx"/>
      </project>
      <attributes>
        <category>Middleware</category>
        <category>CAN</category>
      </attributes>
    </example>

    <example name="USB Device HID" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/USB/Device/HID" version="1.0.0">
      <description>USB Human Interface Device providing access from PC to board LEDs and Joystick</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="HID.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="USB" Cgroup="Device" Csub="HID"/>
        <category>Middleware</category>
        <category>USB Device</category>
        <keyword>HID</keyword>
      </attributes>
    </example>

    <example name="USB Device Mass Storage" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/USB/Device/MassStorage" version="1.0.0">
      <description>USB Mass Storage Device using SD/MMC Memory Card or RAM as storage media</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="MassStorage.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="USB" Cgroup="Device" Csub="MSC"/>
        <category>Middleware</category>
        <category>USB Device</category>
        <keyword>Memory Disk</keyword>
      </attributes>
    </example>

    <example name="USB Device Virtual COM" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/USB/Device/VirtualCOM" version="1.0.0">
      <description>Bridge between PC USB Virtual COM Port and UART port</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="VirtualCOM.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="USB" Cgroup="Device" Csub="CDC"/>
        <category>Middleware</category>
        <category>USB Device</category>
        <keyword>Virtual COM</keyword>
      </attributes>
    </example>

    <example name="USB Host Mass Storage" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/USB/Host/MassStorage" version="1.0.0">
      <description>USB Host file manipulation example: create, read, copy, delete files from USB Mass Storage Device and format the storage device</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="MassStorage.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="USB" Cgroup="Host" Csub="MSC"/>
        <category>Middleware</category>
        <category>USB Host</category>
        <keyword>Mass Storage</keyword>
      </attributes>
    </example>

    <example name="USB Host Keyboard" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/USB/Host/Keyboard" version="1.0.0">
      <description>Measure example using USB HID Keyboard as input device</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="Keyboard.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="USB" Cgroup="Host" Csub="HID"/>
        <category>Middleware</category>
        <category>USB Host</category>
        <keyword>Keyboard</keyword>
      </attributes>
    </example>

    <example name="File System Demo" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/FileSystem/File_Demo" version="1.0.0">
      <description>File manipulation example: create, read, copy, delete files on any enabled drive (SD/MMC Card) and format the drive</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="File_Demo.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="File System" Cgroup="Drive"/>
        <category>Middleware</category>
        <category>File System</category>
        <keyword>SD/MMC Card</keyword>
      </attributes>
    </example>

    <example name="BSD Client" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/Network/BSD_Client" version="1.0.0">
      <description>Example using BSD sockets to send commands to remote server</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="BSD_Client.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Socket" Csub="BSD"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>BSD</keyword>
      </attributes>
    </example>

    <example name="BSD Client IPv4/IPv6" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/NetworkDS/BSD_Client" version="1.0.0">
      <description>Example using BSD sockets to send commands to remote server</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="BSD_Client.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Socket" Csub="BSD"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>BSD</keyword>
      </attributes>
    </example>

    <example name="BSD Server" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/Network/BSD_Server" version="1.0.0">
      <description>Example using BSD sockets to accept commands from remote clients</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="BSD_Server.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Socket" Csub="BSD"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>BSD</keyword>
      </attributes>
    </example>

    <example name="BSD Server IPv4/IPv6" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/NetworkDS/BSD_Server" version="1.0.0">
      <description>Example using BSD sockets to accept commands from remote clients</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="BSD_Server.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Socket" Csub="BSD"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>BSD</keyword>
      </attributes>
    </example>

    <example name="FTP Server" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/Network/FTP_Server" version="1.0.0">
      <description>File Server using FTP protocol with SD/MMC Memory Card as storage media</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="FTP_Server.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="FTP Server"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>FTP</keyword>
      </attributes>
    </example>

    <example name="FTP Server IPv4/IPv6" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/NetworkDS/FTP_Server" version="1.0.0">
      <description>File Server using FTP protocol with SD/MMC Memory Card as storage media</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="FTP_Server.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="FTP Server"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>FTP</keyword>
      </attributes>
    </example>

    <example name="HTTP Server" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/Network/HTTP_Server" version="1.0.0">
      <description>Compact Web Server with CGI interface</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="HTTP_Server.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="Web Server Compact"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>Web Server</keyword>
        <keyword>HTTP</keyword>
      </attributes>
    </example>

    <example name="HTTP Server IPv4/IPv6" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/NetworkDS/HTTP_Server" version="1.0.0">
      <description>Compact Web Server with CGI interface</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="HTTP_Server.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="Web Server Compact"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>Web Server</keyword>
        <keyword>HTTP</keyword>
      </attributes>
    </example>

    <example name="SMTP Client" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/Network/SMTP_Client" version="1.0.0">
      <description>Example showing how to compose and send emails</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="SMTP_Client.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="SMTP Client"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>SMTP</keyword>
      </attributes>
    </example>

    <example name="SMTP Client IPv4/IPv6" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/NetworkDS/SMTP_Client" version="1.0.0">
      <description>Example showing how to compose and send emails</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="SMTP_Client.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="SMTP Client"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>SMTP</keyword>
      </attributes>
    </example>

    <example name="SNMP Agent" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/Network/SNMP_Agent" version="1.0.0">
      <description>Example showing how to use a Simple Network Management Protocol (SNMP)</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="SNMP_Agent.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="SNMP Agent"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>SNMP</keyword>
      </attributes>
    </example>

    <example name="Telnet Server" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/Network/Telnet_Server" version="1.0.0">
      <description>Command-line Host service example using Telnet protocol</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="Telnet_Server.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="Telnet Server"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>Telnet</keyword>
      </attributes>
    </example>

    <example name="Telnet Server IPv4/IPv6" doc="Abstract.txt" folder="Boards/Keil/MCBSTM32C/Middleware/NetworkDS/Telnet_Server" version="1.0.0">
      <description>Command-line Host service example using Telnet protocol</description>
      <board name="MCBSTM32C" vendor="Keil"/>
      <project>
        <environment name="uv" load="Telnet_Server.uvprojx"/>
      </project>
      <attributes>
        <component Cclass="Network" Cgroup="Service" Csub="Telnet Server"/>
        <category>Middleware</category>
        <category>Network</category>
        <keyword>Telnet</keyword>
      </attributes>
    </example>
  </examples>

</package>
