#include "systick.h"

// =============================================================
//                    系统滴答定时器实现文件 (systick.c)
// =============================================================

#include "stm32f10x.h" // STM32F10x系列芯片对应的头文件

// 定义一个用于计数的全局静态变量
static __IO uint32_t sysTick_counter = 0;

/**
 * @brief  配置SysTick定时器为1ms中断一次
 * @param  None
 * @retval None
 */
void SysTick_Init(void) {
    if (SysTick_Config(SystemCoreClock / 1000)) { // 配置为1ms中断
        while (1); // 配置失败则进入死循环
    }
}

/**
 * @brief  SysTick中断服务函数，每1ms调用一次
 * @param  None
 * @retval None
 */
void SysTick_Increment(void) {
    sysTick_counter++; // 全局变量计数器递增
}

/**
 * @brief  获取当前系统运行的毫秒数 (等效于 HAL_GetTick)
 * @param  None
 * @retval uint32_t 系统运行毫秒数
 */
uint32_t GetTick(void) {
    return sysTick_counter;
}

/**
 * @brief  毫秒级延时 (基于GetTick实现)
 * @param  ms: 需要延时的毫秒数
 * @retval None
 */
void Delay_ms(uint32_t ms) {
    uint32_t start_tick = GetTick();
    // 等待循环直到当前计数器与开始计数的差值达到指定的延时时间
    // (uint32_t)(GetTick() - start_tick) < ms 这样写法可以防止 sysTick_counter 溢出重置的问题
    while ((uint32_t)(GetTick() - start_tick) < ms) {
        // 等待
    }
}



